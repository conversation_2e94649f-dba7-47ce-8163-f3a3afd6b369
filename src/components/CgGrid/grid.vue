<script setup lang="ts">
import type { VxeColumnPropTypes, VxeGridProps, VxeGridPropTypes, VxeTableDefines } from 'vxe-table'
import type { GridToolbarConfig, Selection } from './grid'
import { adaptive as adaptiveDirective } from '@/directives/adaptive'
import { Download, RefreshRight } from '@element-plus/icons-vue'
import { ElButton, ElEmpty, ElPagination, ElTooltip } from 'element-plus'
import { computed, onMounted, provide, ref, toRefs, unref, useAttrs, watch } from 'vue'
import { VxeGrid } from 'vxe-table'
import { eachTree, isEmpty } from 'xe-utils'
import { useRequest } from './composables/use-request'
import { useResize } from './composables/use-resize'
import { useSort } from './composables/use-sort'
import { useToolBar } from './composables/use-toolbar'
import { gridInstanceKey } from './constants'
import { gridProps } from './grid'

defineOptions({
  name: 'CgGrid',
})

const props = defineProps(gridProps())

defineSlots<{
  [key: string]: any // 你可以根据你的插槽的实际类型进行更精确的定义
}>()
const { data } = toRefs(props)
const vAdaptive = adaptiveDirective
function createSelectionColumn(columns: VxeGridPropTypes.Columns, selection: Selection[]) {
  selection.forEach((item) => {
    const column = columns.find(el => el.type === item.type)
    if (!column) {
      const selection: VxeTableDefines.ColumnOptions = {
        type: item.type as VxeColumnPropTypes.Type,
        width: item.type === 'seq' ? 44 : item.width || 32,
        resizable: false,
        align: 'center',
        showOverflow: item.type === 'seq',
        fixed: item.fixed ?? null,
      }
      if (item.type === 'seq')
        selection.title = item.title ?? '#'

      columns.unshift(selection)
    }
  })
}

const attrs = useAttrs()
const gridOption = computed<VxeGridProps>(() => {
  const { columns } = props.options
  const selection = props.selection
  if (selection)
    createSelectionColumn(columns || [], selection)
  eachTree(
    columns,
    (column) => {
      if (column.children?.length)
        return
      if (!column.formatter) {
        // 默认添加格式化，把空的值格式化成 -
        column.formatter = (params: {
          cellValue: any
          column: VxeTableDefines.ColumnInfo
          row: any
        }) => {
          const { cellValue } = params
          return cellValue === null || cellValue === undefined || cellValue === '' ? '-' : cellValue
        }
      }
      if (column.title === '操作') {
        column.headerClassName = 'col--operate'
        column.className = 'col--operate'
      }
      if (['ImageUrl', 'imageUrl'].includes(column.field || '')) {
        if (!column.width)
          column.width = 65
        column.resizable = false
      }
    },
    { children: 'children' },
  )
  return {
    showOverflow: 'tooltip',
    height: 'auto',
    border: false,
    columnConfig: {
      resizable: true,
    },
    round: true,
    headerCellConfig: {
      height: 40,
    },
    ...props.options,
    ...unref(attrs),
  }
})

const {
  vxeGridRef,
  loading,
  gridData,
  isEmptyData,
  queryModelBackup,
  currentPage,
  pageSize,
  pageSizes,
  total,
  requestData,
  resetCurrentPage,
  handleSizeChange,
  handleCurrentChange,
} = useRequest(props)

watch(() => data.value.length, () => {
  requestData()
}, { deep: true })

const {
  buttonsSlot,
  toolsSlot,
  toolsLeftSlot,
  toolsRigthSlot,
  buttonsComputed,
  tools,
  exportFile,
  refresh,
  toolsLeftSlotName,
  toolsRightSlotName,
  toolsSlotName,
  buttonsSlotName,
  toolBarVisible,
  hasToolbarContent,
  handleRefresh,
  handleExportFile,
  getButtonProps,
  getButtonComponent,
} = useToolBar(props, requestData, queryModelBackup)
const { sortChange } = useSort(props, requestData)
const { gridWrapperRef } = useResize(vxeGridRef)

function getCheckboxRecords(isFull = false) {
  return vxeGridRef.value?.getCheckboxRecords(isFull)
}
function toggleCheckboxRow(row: any) {
  return vxeGridRef.value?.toggleCheckboxRow(row)
}
function setLoading(value: boolean) {
  loading.value = value
}

provide(gridInstanceKey, vxeGridRef as any)

defineExpose({
  setLoading,
  requestData,
  reload: requestData,
  resetCurrentPage,
  toggleCheckboxRow,
  vxeGridRef,
  getCheckboxRecords,
  hideColumn: (columnOptions: VxeTableDefines.ColumnInfo | VxeTableDefines.ColumnInfo[]) => vxeGridRef.value?.hideColumn(columnOptions),
  showColumn: (columnOptions: VxeTableDefines.ColumnInfo | VxeTableDefines.ColumnInfo[]) => vxeGridRef.value?.showColumn(columnOptions),
})

const isTeleport = ref(false)
onMounted(() => {
  if (props.autoLoad)
    requestData()
  else
    isEmptyData.value = true
  isTeleport.value = true
})
</script>

<template>
  <div ref="gridWrapperRef" class="cg-grid">
    <div v-if="toolBarVisible" class="cg-grid__toolbar" :class="{ 'has-content': hasToolbarContent }">
      <div v-if="!buttonsSlot && buttonsComputed.length" class="cg-grid__buttons">
        <template v-for="(item, index) in buttonsComputed" :key="`button${index}`">
          <Component
            :is="getButtonComponent(item)" v-if="item.visible" v-bind="getButtonProps(index, item)"
            v-on="item.on || {}"
          >
            {{ item.text }}
          </Component>
        </template>
      </div>
      <div v-else>
        <slot :name="buttonsSlotName" />
      </div>
      <div class="cg-grid__tools">
        <div v-if="toolsLeftSlot">
          <slot :name="toolsLeftSlotName" />
        </div>
        <template v-if="!toolsSlot">
          <template v-if="tools && tools.length">
            <template v-for="(item, index) in tools" :key="`tools${index}`">
              <Component
                :is="getButtonComponent(item)" v-if="item.visible" plain class="is-toolbar" :icon="item.icon"
                :title="item.text" v-on="item.on || {}"
              />
            </template>
          </template>
          <Teleport
            v-if="isTeleport && typeof refresh === 'object' && refresh?.teleport"
            :to="typeof refresh === 'object' ? refresh.teleport : undefined"
          >
            <ElTooltip effect="dark" content="刷新" :show-after="200" placement="top">
              <ElButton plain :icon="RefreshRight" class="is-toolbar" @click="handleRefresh" />
            </ElTooltip>
          </Teleport>
          <template v-else>
            <ElTooltip v-if="refresh === true" effect="dark" content="刷新" :show-after="200" placement="top">
              <ElButton plain :icon="RefreshRight" class="is-toolbar" @click="handleRefresh" />
            </ElTooltip>
          </template>
          <Teleport v-if="isTeleport && exportFile?.teleport" :to="exportFile!.teleport">
            <ElTooltip v-if="exportFile" effect="dark" :show-after="200" content="导出" placement="top">
              <ElButton plain :icon="Download" class="is-toolbar" @click="handleExportFile" :disabled="exportFile.disabled"/>
            </ElTooltip>
          </Teleport>
          <template v-else>
            <ElTooltip v-if="exportFile" effect="dark" :show-after="200" content="导出" placement="top">
              <ElButton plain :icon="Download" class="is-toolbar" @click="handleExportFile" :disabled="exportFile.disabled"/>
            </ElTooltip>
          </template>
        </template>
        <div v-else>
          <slot :name="toolsSlotName" />
        </div>
        <div v-if="toolsRigthSlot">
          <slot :name="toolsRightSlotName" />
        </div>
      </div>
    </div>
    <slot name="grid-top" />
    <!-- <div v-adaptive class="cg-grid__wrapper"> -->
    <div class="cg-grid__wrapper">
      <slot name="grid-left" />
      <VxeGrid
        ref="vxeGridRef" v-loading="vxeLoading || disableLoading ? false : loading"
        :loading="vxeLoading ? loading : false" v-bind="gridOption" :data="gridData" @sort-change="sortChange"
      >
        <template v-for="(_, slotName) in $slots" #[slotName]="scope">
          <slot :name="slotName" v-bind="scope" />
        </template>
        <template v-if="paginationVisible" #pager>
          <ElPagination
            v-model:current-page="currentPage" v-model:page-size="pageSize" :small="paginationSmall"
            :background="true" :page-sizes="pageSizes" layout="total, sizes, prev, pager, next, jumper" :total="total"
            @size-change="handleSizeChange" @current-change="handleCurrentChange"
          />
        </template>
        <template v-if="!useSystemEmptyData" #empty>
          <div class="cg-grid__empty" :class="{ 'is-empty': isEmptyData }">
            <ElEmpty description="暂无数据" :image-size="115" />
          </div>
        </template>
        <template v-if="vxeLoading" #loading>
          <i class="vxe-icon-spinner roll text-40" />
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<style lang="scss">
.cg-grid {
  min-height: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &__wrapper {
    display: flex;
    flex-direction: row;
    min-height: 0;
    flex: 1;

    .vxe-grid {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  &__empty {
    color: #888;
    opacity: 0;

    &.is-empty {
      opacity: 1;
    }

    .el-empty {
      --el-empty-padding: 5px;
    }

    .el-empty__description {
      --el-empty-description-margin-top: 5px;
    }
  }

  &__toolbar {
    display: flex;
    justify-content: space-between;

    &.has-content {
      padding: 0 8px 8px 8px;
    }
  }

  &__buttons {
    .el-button+.el-button {
      margin-left: 0;
    }

    display: flex;
    justify-content: center;
    align-items: center;
    gap: 6px;
  }

  &__tools {
    display: flex;
    gap: 5px;
    align-items: center;

    .el-button+.el-button {
      margin-left: 0;
    }
  }

  .vxe-grid--pager-wrapper {
    display: flex;
    justify-content: end;
    align-items: center;
    height: 42px;
    padding: 5px;

    border: 1px solid #e8eaec;
    border-top: unset;
    border-radius: 0 0 4px 4px;
    background-color: #fff;
  }

  .vxe-table--render-wrapper {
    background-color: transparent;
  }

  // .vxe-table--render-default .vxe-header--column {
  //   padding: 10px 0;
  // }

  // .vxe-table .col--last.vxe-body--column .vxe-cell {
  //   padding: 0 4px;
  //   text-overflow: unset;
  // }

  .vxe-table--render-default .vxe-table--body-wrapper {
    // background-color: unset;
    background-color: var(--vxe-ui-layout-background-color);
  }

  .vxe-table--body {
    background-color: #fff;
  }

  .vxe-table .vxe-body--column.col--operate .vxe-cell {
    padding: 0 2px;
    text-overflow: unset;
  }

  .vxe-table .col--operate.vxe-header--column .vxe-cell {
    padding: 0 6px 0 12px;
    text-overflow: unset;
  }

  .vxe-table .vxe-table--header-wrapper .vxe-table--header,
  .vxe-table .vxe-table--header-wrapper .vxe-table--header .vxe-header--row,
  .vxe-table .vxe-table--header-wrapper .vxe-table--header .vxe-header--row th {
    height: 40px;
  }

  .vxe-grid.is--round {
    &:has(.vxe-grid--pager-wrapper) {
      .vxe-table.is--round {
        border-bottom-right-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }

      .vxe-table .vxe-table--border-line {
        border-bottom-right-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }
    }
  }

  .vxe-table.is--round {
    border-radius: 4px;
  }

  .is--round .vxe-table--header-wrapper {
    border-radius: 4px 4px 0 0 !important;
  }

  .is--round .el-loading-mask {
    border-radius: 4px !important;
  }

  // 复制按钮
  .vxe-table .vxe-body--row.row--hover .cg-copy .cg-copy__icon {
    opacity: 1;
  }

  .vxe-table .vxe-header--column .vxe-cell .vxe-cell--title .el-icon {
    vertical-align: sub;
  }

  .vxe-table--render-default .vxe-body--row.is--expand-row {
    background: #eee;

    &.row--current {
      background: #eee;
    }

    &.row--hover {
      background: #e0e0e0;
    }

    .vxe-body--column {
      background-image: unset;
    }
  }

  .vxe-custom--option .vxe-checkbox--icon,
  .vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon,
  .vxe-checkbox .vxe-checkbox--icon {
    color: #9e9e9e;
  }

  .vxe-table--render-default .vxe-cell--checkbox.is--checked,
  .vxe-table--render-default .vxe-cell--checkbox.is--indeterminate {
    .vxe-checkbox--icon {
      color: #64b69d;
    }

    .vxe-checkbox--icon:hover {
      color: #399e96;
    }
  }

  .vxe-table--render-default .vxe-cell--checkbox {
    .vxe-checkbox--icon:hover {
      color: #399e96;
    }
  }

  .vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn {
    color: #000;
    font-weight: bold;
  }

  .vxe-header--column .vxe-cell--required-icon:before {
    top: 0.3em;
  }
}

.el-button.is-toolbar {
  padding: 7px;
  height: 32px;

  i {
    font-size: 16px;
  }

  span {
    display: none;
  }
}

.el-button.is-toolbar+.el-button.is-toolbar {
  margin-left: 0;
}

.vxe-custom--option .vxe-checkbox--icon,
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon,
.vxe-checkbox .vxe-checkbox--icon {
  color: #bfc2c7;
}
</style>
