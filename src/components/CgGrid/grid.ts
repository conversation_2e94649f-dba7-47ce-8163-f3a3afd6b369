import type { ExtractPropTypes, PropType } from 'vue'
import type { VxeColumnPropTypes, VxeGridProps } from 'vxe-table'
import type { DropdownProps } from '../CgElementUI/CgDropdown'
import type { ProxyOption } from '../utils'
import type Grid from './grid.vue'

export interface GridToolbarButtons {
  component?: string | any
  text?: string
  visible?: boolean
  ifRender?: () => boolean
  on?: Record<string, (...args: any[]) => any>
  props?: Record<string, any> | DropdownProps
}

export interface GridToolbarTools extends GridToolbarButtons {
  icon?: any
}

export interface GridToolbarRefresh {
  teleport?: string
}

export interface GridToolbarCustomColumnProps {
  field: string
  visible: string
  index: string
}

export interface GridToolbarExportFile {
  exportProxy: ProxyOption
  permissionProxy?: ProxyOption
  teleport?: string
  disabled?: boolean
}

export type Data<T = any> = T[]

export interface GridToolbarConfig {
  refresh?: GridToolbarRefresh | boolean
  // import?: boolean
  exportFile?: GridToolbarExportFile
  buttons?: GridToolbarButtons[]
  tools?: GridToolbarTools[]
  slots?: {
    /**
     * 工具栏左侧插槽，默认为 buttons
     */
    buttons?: string
    /**
     * 工具栏左侧插槽，默认为 tools
     */
    tools?: string
    /**
     * 工具栏左侧插槽，默认为 tools-left
     */
    toolsLeft?: string
    /**
     * 工具栏右侧插槽，默认为 tools-right
     */
    toolsRight?: string
  }
}

export interface Selection {
  type: 'checkbox' | 'seq'
  fixed?: VxeColumnPropTypes.Fixed
  width?: number
  title?: string
}

export interface SortMap {
  orderBy: string
  orderByColumn: string
}

export type BeforePageSizeChange = (pageIndex?: number) => boolean

export function gridProps() {
  return {
    options: { type: Object as PropType<VxeGridProps>, default: () => ({}) },
    proxyOption: {
      type: Object as PropType<ProxyOption>,
      default: null,
    },
    data: {
      type: Array,
      default: [],
    },
    toolbarConfig: {
      type: Object as PropType<GridToolbarConfig>,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    selection: {
      type: Array as PropType<Selection[]>,
      default: () => [],
    },
    paginationSmall: {
      type: Boolean,
      default: false,
    },
    useSystemEmptyData: {
      type: Boolean,
      default: false,
    },
    /** 使用vxe-table 自带loading，默认为el-loading,表格渲数据多,el-loading状态会卡顿 */
    vxeLoading: {
      type: Boolean,
      default: false,
    },
    /** 禁用loading状态 */
    disableLoading: {
      type: Boolean,
      default: false,
    },
    paginationVisible: {
      type: Boolean,
      default: true,
    },
    beforeLoadData: {
      type: Function as PropType<(rows: any[]) => any | void>,
    },
    /**
     * 转换http请求参数格式，params->{key:1,key:2}
     */
    transformRequestParam: {
      type: Function as PropType<(params: any) => any>,
    },
    sortMap: {
      type: Object as PropType<SortMap>,
    },
    pageSizes: {
      type: Array as PropType<number[]>,
      default: () => [20, 50, 100, 200],
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    beforePageSizeChange: { type: Function as PropType<BeforePageSizeChange>, default: null },
    beforeRefresh: {
      type: Function as PropType<() => (boolean | void | Promise<boolean | void>)>,
      default: null,
    },
  }
}

export type GridProps = Partial<ExtractPropTypes<ReturnType<typeof gridProps>>>

export type GridInstance = InstanceType<typeof Grid>
