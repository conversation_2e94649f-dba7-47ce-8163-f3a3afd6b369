<script setup lang="ts">
import type { MenuItem } from '@/stores/Permission'
import { useMenuStore } from '@/stores/Menu'
import { usePermissionStore } from '@/stores/Permission'
import SideBarItem from './SideBarItem.vue'
import { useUserStore } from '@/stores/User'

const menuStore = useMenuStore()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 状态变量
const tabIndex = ref(0)
const popoverVisible = ref(false)
const popperElm = ref<HTMLElement | null>(null)

// 计时器
let timer = 0
let popperTimer = 0

const { pointer } = menuStore
const { permissions } = userStore
// 根据权限控制 SideBar 菜单项的显示
const menus = computed(() => {
  const permissionSet = new Set(permissions)

  return permissionStore.menus
    .map((menu) => {
      const filteredChildren = (menu.children || []).filter((child) =>
        child.permission ? permissionSet.has(child.permission) : true,
      )
      // 如果子菜单页面都没有权限，则不显示该父菜单
      return filteredChildren.length > 0
        ? {
            ...menu,
            children: filteredChildren,
          }
        : null
    })
    .filter((menu): menu is MenuItem => menu !== null)
})

function getFirstChildRoute(item: MenuItem) {
  if (!item.children?.length)
    return item.id.toString()

  // 查找第一个有效的直接子菜单链接
  for (const child of item.children) {
    if (!child.children && child.link) {
      return child.link
    }
  }

  // 查找第一个有子菜单的菜单项的第一个子菜单
  for (const child of item.children) {
    if (child.children?.length && child.children[0].link) {
      return child.children[0].link
    }
  }

  return item.id.toString()
}

const currentMenusLeaf = computed(() => {
  if (menus.value[tabIndex.value]?.children) {
    return menus.value[tabIndex.value].children?.filter(item => !item.children)
  }
  return []
})

const currentMenusOther = computed(() => {
  if (menus.value[tabIndex.value]?.children) {
    return menus.value[tabIndex.value].children?.filter(item => item.children)
  }
  return []
})

onMounted(() => {
  popperElm.value = document.querySelector('.cg-sideBar__popover')
})

function handlePopperHover() {
  clearTimeout(popperTimer)
}

function routerLinkClick() {
  popoverVisible.value = false
}

function handleHover(index: number, event: MouseEvent) {
  clearTimeout(popperTimer)
  timer = setTimeout(() => {
    clearTimeout(timer)
    calculatePosition(index, event)
  }, 50)
}

function handleLeave() {
  if (timer) {
    clearTimeout(timer)
  }

  popperTimer = setTimeout(() => {
    popoverVisible.value = false
  }, 200)
}

/**
 * 计算菜单弹出框的位置
 */
function calculatePosition(index: number, event: MouseEvent) {
  tabIndex.value = index
  popoverVisible.value = true

  nextTick(() => {
    const target = event.target as HTMLElement

    if (popperElm.value) {
      // 获取菜单项相对于视口的位置
      const rect = target.getBoundingClientRect()
      const popoverHeight = popperElm.value.offsetHeight

      // 计算弹出菜单的顶部位置（相对于视口）
      let top = rect.top
      const clientHeight = document.body.clientHeight

      // 检查是否会超出屏幕底部
      if (top + popoverHeight > clientHeight) {
        // 如果会超出，则将弹出菜单向上移动
        top = clientHeight - popoverHeight
        // 确保不会超出屏幕顶部
        if (top < 0)
          top = 0
      }

      // 设置弹出菜单的位置
      popperElm.value.style.top = `${top}px`
    }
  })
}
</script>

<template>
  <div class="cg-sideBar">
    <div class="cg-sideBar__logo">
      <div class="logo-container">
        <span class="logo-text">C</span>
      </div>
    </div>
    <ElMenu :default-active="pointer.leafId?.toString() || pointer.topId?.toString()" router>
      <ElMenuItem
        v-for="(item, index) in menus" :key="item.id" :index="getFirstChildRoute(item)"
        :class="{ 'is-active': item.id === pointer.topId }"
        @mouseenter="(event: MouseEvent) => handleHover(index, event)" @mouseleave="handleLeave"
      >
        <div class="menu-hover" />
        <a>
          <transition name="icon-scale" mode="out-in">
            <font-awesome-icon v-if="item.icon" :key="item.icon" :icon="item.icon" />
            <font-awesome-icon v-else key="default-icon" icon="fa-box" />
          </transition>
          <span>{{ item.menu }}</span>
        </a>
      </ElMenuItem>
    </ElMenu>
  </div>
  <Teleport to="body">
    <Transition name="menu-popover-fade">
      <div
        v-show="popoverVisible" ref="popperElm" class="cg-sideBar__popover menu-pop is-vertical"
        :class="{ 'is-hidden': !popoverVisible }" @mouseenter="handlePopperHover" @mouseleave="handleLeave"
      >
        <div v-if="currentMenusLeaf?.length" class="cg-sideBar-submenu__block">
          <SideBarItem
            v-for="item in currentMenusLeaf" :key="item.id" :tab-index="tabIndex" :item="item"
            @router-link-click="routerLinkClick"
          />
        </div>
        <div v-for="item in currentMenusOther" :key="item.id" class="cg-sideBar-submenu__block">
          <SideBarItem :tab-index="tabIndex" :item="item" />
          <SideBarItem
            v-for="item1 in item.children" :key="item1.id" :tab-index="tabIndex" :item="item1"
            @router-link-click="routerLinkClick"
          />
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style src="./index.scss" lang="scss" />

<style scoped>
.icon-scale-enter-active,
.icon-scale-leave-active {
  transition: all 0.3s ease;
}

.icon-scale-enter-from,
.icon-scale-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>
