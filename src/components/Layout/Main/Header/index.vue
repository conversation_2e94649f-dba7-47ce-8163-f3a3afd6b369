<script setup lang="ts">
import { useCacheStore } from '@/stores/Cache'
import { usePermissionStore } from '@/stores/Permission'
import { useUserStore } from '@/stores/User'
import { UserFilled } from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import TagsView from '../TagsView/TagsView.vue'

const mainThemeName = ref('')
const router = useRouter()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const cacheStore = useCacheStore()
// const avatar = avatarImg
const avatar = ''

const menuItems = computed(() => {
  return {
    text: userStore.name,
    items: [
      {
        text: '账号中心',
        id: 1,
        // roles: [permissionKeys.setting.acountInfo.info],
      },
      {
        text: '退出登录',
        id: 3,
      },
    ],
  }
})

onMounted(() => {
  initThemeColor()
})

function initThemeColor() {
  mainThemeName.value = localStorage.getItem('theme') || ''
}

async function menuSelect(args: any) {
  const id = args.id
  switch (id) {
    case 1:
      router.push('/settings/accountCenter')
      break
    case 3:
      await userStore.logout()
      // 清理权限和缓存状态
      permissionStore.resetToken()
      cacheStore.resetCacheInfo()
      break
  }
}
</script>

<template>
  <div class="cg-header">
    <div class="cg-header__left">
      <div class="cg-header__logo">
        <!-- <img src="../../../../assets/images/layout/logo.svg"> -->
      </div>
      <TagsView />
    </div>
    <div class="cg-header__right">
      <div class="download-center-icon relative">
        <CgLinkButton to="/download-center" type="primary" link size="small">
          <font-awesome-icon :icon="['far', 'circle-down']" class="text-xl text-white" />
        </CgLinkButton>
      </div>
      <ElAvatar :size="23" :src="avatar" :icon="UserFilled" />
      <CgDropdown :options="menuItems" class="cg-header__account" @command="menuSelect" />
    </div>
  </div>
</template>

<style src="./index.scss" lang="scss" />
