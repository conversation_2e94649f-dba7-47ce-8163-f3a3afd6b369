<script setup lang="ts">
import type { ExportTaskListVO, ExportTaskPageReq } from '@/apiv2/file'
import type { PageListInstance, PageListProps } from '@/components/CgPageList'
import { createApiProxy } from '@/api.services/api.factory'
import { exportTaskApi } from '@/api.services/file.service'
import { Configuration, ExportTaskListVOExportBizTypeEnum, ExportTaskListVOStatusEnum, 文件信息APIApi } from '@/apiv2/file'
import ComponentTagName from '@/common/component-tag-name'
import { GridCellFormatName, GridCellRenderName } from '@/components/CgGrid'
import { BUTTON_PERMISSION } from '@/enums/permission'
import { usePermissionStore } from '@/stores/Permission'
import { ElNotification } from 'element-plus'

defineOptions({
  name: 'DownloadCenter',
})

const { hasButtonPermission } = usePermissionStore()

// 判断下载按钮是否禁用
function isBtnDisabled(row: ExportTaskListVO): boolean {
  return !hasButtonPermission(BUTTON_PERMISSION.FILE_DOWNLOAD) || row.status !== ExportTaskListVOStatusEnum.COMPLETED
}

// 创建文件信息API服务实例
const fileInfoApi = createApiProxy('/gaia-file', { Configuration, API: 文件信息APIApi })

const pageListRef = ref<PageListInstance | null>(null)
const queryModel = ref<ExportTaskPageReq>({
  pageNum: 1,
  pageSize: 20,
  createStartTime: undefined,
  createEndTime: undefined,
})

async function handleDownload(done: () => void, row: ExportTaskListVO) {
  if (!row || !row.exportPath) {
    ElMessage.error('文件路径不存在')
    return
  }

  try {
    const fileName = row.fileName || '导出文件.xlsx'

    // 获取签名URL
    const response = await fileInfoApi.getSignedUrl({ filePath: row.exportPath })

    if (response.data?.success && response.data.data?.url) {
      const downloadFrame = document.createElement('iframe')
      downloadFrame.style.display = 'none'
      document.body.appendChild(downloadFrame)

      try {
        const downloadUrl = response.data.data.url

        // 使用iframe导航到下载URL
        if (downloadFrame.contentWindow) {
          downloadFrame.src = downloadUrl

          // 3秒后关闭通知并移除iframe
          setTimeout(() => {
            document.body.removeChild(downloadFrame)
          }, 3000)
          ElNotification({
            title: '下载成功',
            message: `${fileName} 已下载`,
            type: 'success',
            duration: 3000,
          })
        }
      }
      catch (fetchError) {
        console.error('下载文件时出错:', fetchError)
        document.body.removeChild(downloadFrame)

        // 回退到传统下载方法
        const link = document.createElement('a')
        link.href = response.data.data.url
        link.download = fileName
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    }
    else {
      ElMessage.error('获取下载链接失败')
    }
  }
  catch (error) {
    console.error('下载文件时出错:', error)
  }
  finally {
    done()
  }
}

const gridOptions = reactive<PageListProps>({
  gridOption: {
    options: {
      columns: [
        {
          field: '',
          type: 'checkbox',
          width: '3%',
          minWidth: 80,
          fixed: 'left',
          resizable: false,
        },
        {
          field: 'id',
          title: '编号',
          minWidth: 120,
          width: '18%',
        },
        {
          field: 'fileName',
          title: '报告名称',
          minWidth: 100,
          width: '20%',
        },
        {
          field: 'exportBizType',
          title: '类型',
          minWidth: 80,
          width: '10%',
          formatter: ({ cellValue }: { cellValue: ExportTaskListVOExportBizTypeEnum }) => {
            if (cellValue === ExportTaskListVOExportBizTypeEnum.PRODUCT_EXPORT) {
              return '产品管理'
            }
            if (cellValue === ExportTaskListVOExportBizTypeEnum.COMMODITY_SHOP_LINE_EXPORT) {
              return '商品导出到ShopLine'
            }
            return '未知类型'
          },
        },
        {
          field: 'createTime',
          title: '下载时间',
          minWidth: 100,
          width: '15%',
          formatter: [GridCellFormatName.FormatDate, 'YYYY-MM-DD HH:mm'],
        },
        {
          field: 'finishTime',
          title: '生成时间',
          minWidth: 100,
          width: '15%',
          formatter: [GridCellFormatName.FormatDate, 'YYYY-MM-DD HH:mm'],
        },
        {
          field: 'status',
          title: '状态',
          minWidth: 100,
          width: '9%',
          cellRender: {
            name: GridCellRenderName.Status,
            props: {
              options: [
                {
                  value: ExportTaskListVOStatusEnum.FAILURE,
                  label: '处理失败',
                  class: 'text-red-500',
                },
                {
                  value: ExportTaskListVOStatusEnum.PENDING,
                  label: '待处理',
                  class: 'text-orange-500',
                },
                {
                  value: ExportTaskListVOStatusEnum.PROCESSING,
                  label: '生成中',
                  class: 'text-blue-500',
                },
                {
                  value: ExportTaskListVOStatusEnum.COMPLETED,
                  label: '已完成',
                  class: 'text-green-500',
                },
              ],
            },
          },
        },
        {
          field: '',
          title: '操作',
          minWidth: 90,
          width: '10%',
          slots: {
            default: 'downloadTemp',
          },
        },
      ],
      round: true,
      showOverflow: true,
    },
    toolbarConfig: {
      refresh: {
        teleport: '.cg-page-header__right',
      },
    },
    proxyOption: {
      request: exportTaskApi.listByPage,
      postParam: 'exportTaskPageReq',
    },
  },
  maxQueryFormVisibleCount: 8,
  searchMoreDisplayMode: 'icon',
  queryForm: [
    {
      component: ComponentTagName.ComplexInput,
      vmodel: {
        modelValue: 'date',
        modelSelect: 'searchTimeType',
        start: 'createStartTime',
        end: 'createEndTime',
      },
      props: {
        type: 'date',
        selectOptions: [
          { value: 1, label: '下载时间' },
        ],
      },
    },
  ],
  autoLoad: true,
})
</script>

<template>
  <div class="h-full flex flex-col">
    <CgPageList ref="pageListRef" v-model:query-model="queryModel" v-bind="gridOptions">
      <template #header-right>
        <span style="display:none" />
      </template>
      <template #downloadTemp="{ row }">
        <CgButton
          type="primary" text :disabled="isBtnDisabled(row)" auto-loading
          @click="handleDownload($event, row)"
        >
          下载
        </CgButton>
      </template>
    </CgPageList>
  </div>
</template>

<style scoped></style>
