<script setup lang="ts">
import type { ExportTaskReq } from '@/apiv2/file'
import type {
  ProductBatchPropertiesUpdateRequest,
  ProductInitTicketCreateReqVO,
  ProductInitTicketCreateResultDetailVO,
  ProductListVO,
  ProductSearchParam,
} from '@/apiv2/product'
import type { GridProps } from '@/components/CgGrid'
import type { PageListInstance, PageListProps } from '@/components/CgPageList'
import type { VxeGlobalRendererHandles, VxeGridPropTypes } from 'vxe-table'
import { exportTaskApi } from '@/api.services/file.service'
import { dictApi, productApi, propertyApi, supplierApi } from '@/api.services/product.service'
import userApi from '@/api.services/user.service'
import { ExportTaskReqExportBizTypeEnum } from '@/apiv2/file'
import { TicketInstListItemVOStatusEnum } from '@/apiv2/product'
import ComponentTagName from '@/common/component-tag-name'
import CgCategoryTree from '@/components/CgCategoryTree'
import CgComplexInputV2 from '@/components/CgElementUI/CgComplexInputV2'
import CgInputNumber from '@/components/CgElementUI/CgInputNumber'
import { GridCellFormatName, GridCellRenderName } from '@/components/CgGrid'
import CgOperateLog from '@/components/CgOperateLog/OperateLog.vue'
import { transformDictData, transformPropertyData } from '@/components/utils'
import { ProductOperationEnum } from '@/enums/product'
import router from '@/router'
import { useProductStore } from '@/stores/Product'
import { ElMessage, ElMessageBox } from 'element-plus'
import XEUtils from 'xe-utils'
import PriorityDialog from './components/PriorityDialog.vue'
import ProductPropertiesDisplay from './components/ProductPropertiesDisplay.vue'
import PropertiesDialog from './components/PropertiesDialog.vue'
import TicketCreateResultDialog from './components/TicketCreateResultDialog.vue'
import { useProductStatus } from './composables/useProductStatus'
import { usePermissionStore } from '@/stores/Permission'
import { BUTTON_PERMISSION } from '@/enums/permission'

defineOptions({
  name: 'ProductList',
})

defineEmits<{
  reset: []
  'update:queryModel': [value: ExtendedProductSearchParam]
}>()

// 扩展搜索参数接口
interface ExtendedProductSearchParam extends Omit<ProductSearchParam, 'status'> {
  searchField?: 'productCodes' | 'skuCodes' | 'productCode' | 'skuCode' | 'productName'
  keyword?: string
  page?: number
  size?: number
  status?: number
  categoryIds: number[]
  supplierIds: number[]
  propertyIds: number[]
  createTimeStart?: string
  createTimeEnd?: string
  maintainerIds: number[]
  assemblyIds: number[]
  electronicState?: boolean
  productName?: string
  productCode?: string
  productCodes: string[]
  skuCode?: string
  skuCodes: string[]
}

// 产品导出请求参数接口
interface ProductExportRequest {
  conditionalExportParam?: ProductExportParam
  exportProductIds?: number[]
}

// 产品导出条件参数接口
interface ProductExportParam {
  categoryIds?: number[]
  supplierIds?: number[]
  propertyValueIds?: number[]
  createTimeStart?: string
  createTimeEnd?: string
  maintainerIds?: number[]
  assemblyIds?: number[]
  status?: number
  electronicState?: boolean
  productName?: string
  productCodes?: string[]
  productCode?: string
  skuCodes?: string[]
  skuCode?: string
  materialIds?: number[]
}

const pageListRef = ref<PageListInstance | null>(null)
const propertiesDialogRef = ref<InstanceType<typeof PropertiesDialog>>()
const selectedProperties = ref<number[]>([])

// 引入按钮鉴权
const { hasButtonPermission } = usePermissionStore()

// 初始化产品状态管理
const { updateProductStatus, deleteProduct, batchDeleteProducts } = useProductStatus(pageListRef)

// 处理标签选择变更
function handlePropertiesChange(value: number[]) {
  selectedProperties.value = value
  confirmBatchUpdateProperties()
}
const queryModel = ref<ExtendedProductSearchParam>({
  page: 1,
  size: 20,
  categoryIds: [],
  supplierIds: [],
  propertyIds: [],
  createTimeStart: undefined,
  createTimeEnd: undefined,
  maintainerIds: [],
  assemblyIds: [],
  electronicState: undefined,
  status: undefined,
  productName: undefined,
  productCode: undefined,
  productCodes: [],
  skuCode: undefined,
  skuCodes: [],
})

function prepareSearchParams(
  rawParams: ExtendedProductSearchParam & { page?: number; size?: number },
): ProductSearchParam {
  const params = { ...rawParams }

  // 映射搜索字段
  if (params.searchField && params.keyword?.trim()) {
    const targetField = params.searchField
    const keywordValue = params.keyword.trim()
    // 根据字段类型决定赋值格式
    if (targetField === 'productCodes' || targetField === 'skuCodes') {
      if (!(params as Record<string, any>)[targetField]?.length) {
        ;(params as Record<string, any>)[targetField] = [keywordValue]
      }
    } else {
      ;(params as Record<string, any>)[targetField] = keywordValue
    }
    delete params.searchField
    delete params.keyword
  }

  // 清理空值
  ;(Object.keys(params) as Array<keyof typeof params>).forEach((key) => {
    if (key === 'page' || key === 'size') {
      return
    }
    const value = params[key]
    if (
      value === undefined ||
      String(value) === '' ||
      (Array.isArray(value) && value.length === 0)
    ) {
      delete params[key]
    }
  })

  return params as ProductSearchParam
}

const currentEditData = ref<any>({})

const operateLogVisible = ref(false)
const operateLogId = ref<number | null>(null)

// 工单创建相关状态
const priorityDialogVisible = ref(false)
const isMultipleProducts = ref(false)
const selectedProductIds = ref<number[]>([])
const selectedRows = ref<ProductListVO[]>([])
watch(
  operateLogVisible,
  (newVal) => {
    if (!newVal) {
      operateLogId.value = null
    }
  },
  { immediate: true },
)
function operatorCmd(
  params: VxeGlobalRendererHandles.RenderTableCellParams,
  cmd: { id: string; text: string },
) {
  const row = params.row
  switch (cmd.id) {
    case ProductOperationEnum.VIEW_DETAIL:
      router.push({
        name: 'ProductDetail',
        params: {
          id: row.id,
        },
      })
      break
    case ProductOperationEnum.EDIT:
      currentEditData.value = { ...row }
      router.push({
        name: 'ProductEdit',
        params: {
          id: row.id,
        },
      })
      break
    case ProductOperationEnum.DELETE:
      deleteProduct(row.id)
      break
    case ProductOperationEnum.TAKE_OFF_SHELF:
      updateProductStatus(row.id, 4, '下架')
      break
    case ProductOperationEnum.PUT_ON_SHELF:
      updateProductStatus(row.id, 3, '上架')
      break
    case ProductOperationEnum.OPERATE_LOG:
      operateLogVisible.value = true
      operateLogId.value = row.id
      break
    case ProductOperationEnum.GENERATE_WORK_ORDER:
      openPriorityDialog([row.id], [row], false)
      break
  }
}

const gridOptions = reactive<PageListProps>({
  gridOption: {
    options: {
      columns: [
        {
          field: 'id',
          type: 'checkbox',
          width: 36,
          fixed: 'left',
          resizable: false,
        },
        {
          type: 'expand',
          slots: {
            content: 'contentTemp',
          },
          width: 36,
          fixed: 'left',
          resizable: false,
        },
        {
          field: 'thumbnail',
          title: '图片',
          width: 60,
          cellRender: {
            name: GridCellRenderName.ImageUrl,
          },
        },
        {
          field: 'productCode',
          title: 'SPU',
          width: 160,
          cellRender: {
            name: GridCellRenderName.Text,
            props: {
              copy: true,
              click: (_value: string, row: ProductListVO) => {
                if(!hasButtonPermission(BUTTON_PERMISSION.PRODUCT_DETAIL)){
                  return ElMessage.error('您没有访问该页面的权限')
                }
                router.push({
                  name: 'ProductDetail',
                  params: {
                    id: row.id,
                  },
                })
              },
            },
          },
        },
        {
          field: 'properties',
          title: '产品标签',
          minWidth: 260,
          slots: {
            default: 'propertiesTemp',
          },
        },
        {
          field: 'productName',
          title: '款名',
          width: 160,
        },
        {
          field: 'categories',
          title: '产品类目',
          width: 160,
          slots: {
            default: 'categoriesTemp',
          },
        },
        {
          field: 'status',
          title: '状态',
          width: 100,
          cellRender: {
            name: GridCellRenderName.Status,
            props: {
              options: [
                {
                  value: 0,
                  label: '草稿',
                  class: 'text-gray-400',
                },
                {
                  value: 1,
                  label: '待审核',
                  class: 'text-orange-500',
                },
                {
                  value: 2,
                  label: '已审核',
                  class: 'text-blue-500',
                },
                {
                  value: 3,
                  label: '已上架',
                  class: 'text-green-500',
                },
                {
                  value: 4,
                  label: '已下架',
                  class: 'text-gray-500',
                },
                {
                  value: 5,
                  label: '已删除',
                  class: 'text-red-500',
                },
              ],
            },
          },
        },
        {
          field: 'supplier',
          title: '供应商',
          width: 120,
          formatter: ({ cellValue }) => cellValue?.supplierName,
        },
        {
          field: 'maintainer',
          title: '产品开发负责人',
          width: 120,
          formatter: ({ cellValue }) => cellValue?.username,
        },
        {
          field: 'ticketInsts',
          title: '关联工单',
          width: 140,
          cellRender: {
            name: GridCellRenderName.DropdownGrid,
            props: {
              key: 'wfInstId',
              options: [
                {
                  field: 'wfInstId',
                  title: '工单ID',
                  width: 134,
                },
                { field: 'ticketDefName', title: '工单类型', width: 120 },
                {
                  field: 'status',
                  title: '工单状态',
                  width: 100,
                  formatter: ({ cellValue }: { cellValue: TicketInstListItemVOStatusEnum }) =>
                    cellValue === TicketInstListItemVOStatusEnum.PROCESSING ? '进行中' : '已关闭',
                },
                {
                  field: 'createTime',
                  title: '创建时间',
                  width: 180,
                  formatter: [GridCellFormatName.FormatDate, 'YYYY-MM-DD HH:mm'],
                },
                { field: 'creator.name', title: '创建人', width: 100 },
              ] as unknown as VxeGridPropTypes.Columns[],
            },
          },
        },
        {
          field: '',
          title: '操作',
          width: 110,
          fixed: 'right',
          cellRender: {
            name: GridCellRenderName.Operate,
            props: {
              items: [
                {
                  text: '详情',
                  id: ProductOperationEnum.VIEW_DETAIL,
                  disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_DETAIL),

                },
                {
                  text: '操作',
                  items: [
                    {
                      text: '编辑',
                      id: ProductOperationEnum.EDIT,
                      disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_UPDATE),
                    },
                    {
                      text: '生成工单',
                      id: ProductOperationEnum.GENERATE_WORK_ORDER,
                      ifRender: (params: any) => {
                        return params.row.status === 0
                      },
                      disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_CREATE_TICKET),
                    },
                    {
                      text: '删除',
                      id: ProductOperationEnum.DELETE,
                      ifRender: (params: any) => {
                        return params.row.status === 0
                      },
                      disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_BATCH_UPDATE_STATUS),
                    },
                    {
                      text: '下架',
                      id: ProductOperationEnum.TAKE_OFF_SHELF,
                      ifRender: (params: any) => {
                        return params.row.status === 3
                      },
                      disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_BATCH_UPDATE_STATUS),
                    },
                    {
                      text: '上架',
                      id: ProductOperationEnum.PUT_ON_SHELF,
                      ifRender: (params: any) => {
                        return params.row.status === 4
                      },
                      disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_BATCH_UPDATE_STATUS),
                    },
                    {
                      text: '操作日志',
                      id: ProductOperationEnum.OPERATE_LOG,
                      disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_LOG_QUERY),
                    },
                  ],
                },
              ],
            },
            events: { command: operatorCmd },
          },
        },
      ],
      round: true,
      showOverflow: true,
      expandConfig: {
        padding: true,
      },
    },
    toolbarConfig: {
      exportFile: {
        exportProxy: {
          request: async (params) => {
            return handleExportProducts(params)
          },
        },
        disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_EXPORT),
      },
      refresh: true,
      buttons: [
        {
          text: '产品建档',
          on: {
            click: () => {
              router.push('./create')
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_CREATE),
          },
        },
        {
          text: '批量导入建档',
          on: {
            click: () => {
              ElMessage.info('批量导入建档功能待实现')
            },
          },
        },
        {
          text: '发起工单',
          on: {
            click: () => {
              handleBatchCreateTickets()
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_CREATE_TICKET),
          },
        },
        {
          text: '编辑标签',
          on: {
            click: () => {
              handleBatchEditProperties()
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_BATCH_UPDATE_PROPERTIES),
          },
        },
        {
          text: '批量删除',
          on: {
            click: () => {
              handleBatchDelete()
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.PRODUCT_BATCH_UPDATE_STATUS),
          },
        },
      ],
    },
    proxyOption: {
      request: productApi.listProducts,
      postParam: 'productSearchParam',
    },
    beforeLoadData: (data: ProductListVO[]) => {
      data.forEach((item) => {
        const childGridOptions: GridProps = {
          options: {
            columns: [
              {
                field: 'thumbnail',
                title: '图片',
                width: 60,
                cellRender: {
                  name: GridCellRenderName.ImageUrl,
                },
              },
              {
                field: 'skuCode',
                title: 'SKU',
                width: 160,
              },
              {
                field: 'properties',
                title: '产品标签',
                minWidth: 260,
                slots: {
                  default: 'childPropertiesTemp',
                },
              },
              {
                field: 'color',
                title: '颜色',
                width: 80,
                formatter: ({ cellValue }) => cellValue?.valueName,
              },
              {
                field: 'specs',
                title: '尺寸',
                minWidth: 180,
                slots: {
                  default: 'packageSizeTemp',
                },
              },
              {
                field: 'netWeight',
                title: '净重',
                minWidth: 100,
                formatter: ({ cellValue }) => {
                  if (!cellValue) return '-'
                  return `${XEUtils.toFixed(cellValue / 1000, 3)} kg`
                },
              },
              {
                field: 'priMatLv1',
                title: '主材质(一级)',
                minWidth: 90,
                formatter: ({ cellValue }) => cellValue?.valueName || '-',
              },
              {
                field: 'priMatLv2',
                title: '主材质(二级)',
                minWidth: 90,
                formatter: ({ cellValue }) => cellValue?.valueName || '-',
              },
              {
                field: 'secMatLv1',
                title: '次级材质(一级)',
                minWidth: 110,
                formatter: ({ cellValue }) => cellValue?.valueName || '-',
              },
              {
                field: 'secMatLv2',
                title: '次级材质(二级)',
                minWidth: 110,
                formatter: ({ cellValue }) => cellValue?.valueName || '-',
              },
              {
                field: 'electronicCertification',
                title: '带电认证',
                width: 120,
                formatter: ({ cellValue }) => (cellValue ? cellValue.valueName : '-'),
              },
              {
                field: 'electronicState',
                title: '带电',
                width: 80,
                formatter: ({ cellValue }) => (cellValue ? '带电' : '不带电'),
              },
              {
                field: 'purchasePrice',
                title: '采购单价(含税含运)',
                width: 140,
                formatter: GridCellFormatName.FormatAmount,
              },
              {
                field: 'status',
                title: '状态',
                width: 80,
                formatter: ({ cellValue }) => (cellValue ? '启用' : '禁用'),
              },
              {
                field: 'createTime',
                title: '创建时间',
                minWidth: 140,
                formatter: [GridCellFormatName.FormatDate, 'YYYY-MM-DD HH:mm'],
              },
            ],
            data: [],
            height: undefined,
          },
          paginationVisible: false,
        }
        childGridOptions.data = item?.skus || []
        ;(item as any).childGridOptions = childGridOptions
      })
    },
    transformRequestParam: prepareSearchParams,
  },
  maxQueryFormVisibleCount: 5,
  searchMoreDisplayMode: 'icon',
  queryForm: [
    {
      component: markRaw(CgCategoryTree),
      vmodel: 'categoryIds',
      props: {
        placeholder: '全部类目',
        showCheckbox: true,
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'supplierIds',
      props: {
        placeholder: '请选择供应商',
        proxyOption: {
          request: () => supplierApi.listSuppliers(),
          transformResponse: (response: any) => response.data.data,
        },
        filterable: true,
        multiple: true,
        props: {
          label: 'supplierName',
          value: 'id',
        },
      },
    },
    {
      component: ComponentTagName.ComplexInput,
      vmodel: {
        modelValue: 'date',
        modelSelect: 'searchTimeType',
        start: 'createTimeStart',
        end: 'createTimeEnd',
      },
      props: {
        type: 'date',
        clearable: false,
        defaultSelectRange: { type: 'month', rang: -1 },
        selectOptions: [{ value: 1, label: '创建时间' }],
      },
    },
    {
      component: ComponentTagName.ComplexInput,
      vmodel: {
        modelValue: 'keyword',
        modelSelect: 'searchField',
        productCodes: 'productCodes',
        skuCodes: 'skuCodes',
      },
      props: {
        type: 'input',
        clearable: true,
        placeholder: '请输入',
        selectOptions: [
          { value: 'productCode', label: 'SPU' },
          { value: 'skuCode', label: 'SKU' },
          { value: 'productName', label: '款名' },
        ],
        batchSearchEnabled: true,
        batchSearchPlaceholder: '请输入多个编码，每行一个',
        batchSearchFields: ['productCode', 'skuCode'],
        batchFieldsMap: {
          productCode: 'productCodes',
          skuCode: 'skuCodes',
        },
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'propertyIds',
      props: {
        placeholder: '产品标签',
        proxyOption: {
          request: () => propertyApi.listProperties(),
          transformResponse: (response: any) => response.data.data,
        },
        groupable: true,
        filterable: true,
        multiple: true,
        transform: transformPropertyData,
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'materialIds',
      label: '主材质',
      props: {
        placeholder: '请选择',
        proxyOption: {
          request: () => dictApi.listDicts({ codes: 'Primar-material-level-one' }),
          transformResponse: (response: any) => response.data.data,
        },
        transform: transformDictData,
        filterable: false,
        multiple: true,
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'electronicState',
      label: '带电',
      props: {
        data: [
          { label: '带电', value: true },
          { label: '不带电', value: false },
        ],
        placeholder: '请选择',
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'assemblyIds',
      label: '商品组装',
      props: {
        placeholder: '请选择',
        proxyOption: {
          request: () => dictApi.listDicts({ codes: 'assembly' }),
          transformResponse: (response: any) => response.data.data,
        },
        transform: transformDictData,
        filterable: false,
        multiple: true,
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'maintainerIds',
      label: '产品开发负责人',
      props: {
        placeholder: '请选择',
        multiple: true,
        proxyOption: {
          request: userApi.search,
          query: {
            pageNum: 1,
            pageSize: 10000,
          },
          postParam: 'userSearchReq',
        },
        filterable: true,
        props: {
          label: 'name',
          value: 'id',
        },
      },
    },
  ],
  autoLoad: true,
})

const tabs = ref([
  { label: '全部', value: 'all' },
  { label: '草稿', value: 'draft' },
  { label: '待上架', value: 'pending' },
  { label: '已上架', value: 'inStock' },
  { label: '已下架', value: 'notInStock' },
])
const activeTab = ref('all')

const productStore = useProductStore()

onMounted(() => {
  productStore.setRefreshFn(() => {
    nextTick(() => {
      pageListRef.value?.reload()
    })
  })
})

/**
 * 批量编辑产品标签
 */
async function handleBatchEditProperties() {
  const selectedRows = pageListRef.value?.getCheckboxRecords() || []
  if (selectedRows.length === 0) {
    ElMessage.warning('请至少选择一个产品进行标签编辑')
    return
  }

  selectedProperties.value = []

  propertiesDialogRef.value?.openDialog()
}

// 确认批量更新标签
async function confirmBatchUpdateProperties() {
  const selectedRows = pageListRef.value?.getCheckboxRecords() || []
  if (selectedRows.length === 0) {
    ElMessage.warning('请至少选择一个产品进行标签编辑')
    return
  }

  const productIds = selectedRows.map((row) => row.id)

  const params: ProductBatchPropertiesUpdateRequest = {
    productIds,
    properties: selectedProperties.value,
  }

  const response = await productApi.batchUpdateProperties({
    productBatchPropertiesUpdateRequest: params,
  })
  if (response.data.success) {
    ElMessage.success('批量更新标签成功')
    pageListRef.value?.reload()
  }
}

async function handleBatchDelete() {
  const selectedRows = pageListRef.value?.getCheckboxRecords() || []
  if (selectedRows.length === 0) {
    ElMessage.warning('请至少选择一个产品进行删除')
    return
  }

  const productIds = selectedRows.map((row) => row.id)
  await batchDeleteProducts(productIds)
}

/**
 * 打开优先级选择对话框
 * @param productIds 产品ID数组
 * @param rows 产品行数据
 * @param isMultiple 是否为批量操作
 */
function openPriorityDialog(productIds: number[], rows: ProductListVO[], isMultiple: boolean) {
  if (productIds.length === 0) {
    ElMessage.warning('请先选择产品')
    return
  }

  // 保存产品ID和操作类型
  selectedProductIds.value = productIds
  selectedRows.value = rows
  isMultipleProducts.value = isMultiple

  // 显示优先级选择对话框
  priorityDialogVisible.value = true
}

const resultDialogVisible = ref(false)
const ticketResultDetail = ref<ProductInitTicketCreateResultDetailVO | null>(null)
// 存储产品ID和产品代码的映射关系
const productCodeMap = ref<Record<string, string>>({})

async function handlePriorityConfirm(priority: number, done: () => void) {
  try {
    const productIds = selectedRows.value.map((r) => r.id!)
    const totalSelected = productIds.length
    const res = await productApi.batchCreateTickets({
      productInitTicketCreateReqVO: { productIds, priority },
    })

    if (!res.data.success) throw new Error(res.data.message)

    const detail = res.data.data as ProductInitTicketCreateResultDetailVO
    const successCnt = Object.keys(detail.succeeded ?? {}).length

    if (successCnt === totalSelected) {
      ElMessage.success(`已成功为 ${successCnt} 个产品创建工单`)
      pageListRef.value?.reload()
      return
    }

    productCodeMap.value = {}
    selectedRows.value.forEach((r) => {
      if (r.id && r.productCode) {
        productCodeMap.value[r.id.toString()] = r.productCode
      }
    })

    selectedRows.value
      .filter((r) => detail.failed?.[r.id!])
      .forEach((r) => {
        ;(detail.failed![r.id!] as any).productCode = r.productCode
      })

    ticketResultDetail.value = detail
    resultDialogVisible.value = true

    if (successCnt > 0) {
      pageListRef.value?.reload()
    }
  } catch (e) {
    console.error(e)
  } finally {
    done()
  }
}

/**
 * 批量创建产品开发工单
 */
function handleBatchCreateTickets() {
  // 获取选中的产品ID
  const selectedRows = pageListRef.value?.getCheckboxRecords() || []
  const selectedIds = selectedRows.map((item: any) => item.id)

  // 打开优先级选择对话框
  openPriorityDialog(selectedIds, selectedRows, true)
}

async function handleExportProducts(_params: any) {
  try {
    // 获取当前的查询条件
    const currentParams = prepareSearchParams(queryModel.value)

    // 构建导出请求参数
    const exportRequest: ProductExportRequest = {}

    // 获取选中的产品记录
    const selectedRecords = pageListRef.value?.getCheckboxRecords() || []

    // 如果有选中的产品，则使用选中的ID进行导出
    if (selectedRecords.length > 0) {
      // 过滤掉可能的 undefined 值并确保 ID 是数字类型
      exportRequest.exportProductIds = selectedRecords
        .map((record: ProductListVO) => record.id)
        .filter((id): id is number => id !== undefined)
    }
    // 否则使用当前的查询条件进行导出
    else {
      const exportParam: ProductExportParam = {}

      // 安全地添加参数，避免类型错误
      if (currentParams.categoryIds && currentParams.categoryIds.length > 0)
        exportParam.categoryIds = currentParams.categoryIds as number[]

      if (currentParams.supplierIds && currentParams.supplierIds.length > 0)
        exportParam.supplierIds = currentParams.supplierIds as number[]

      // 属性值ID列表 - 从propertyIds映射
      if (queryModel.value.propertyIds && queryModel.value.propertyIds.length > 0)
        exportParam.propertyValueIds = queryModel.value.propertyIds

      if (currentParams.createTimeStart) exportParam.createTimeStart = currentParams.createTimeStart

      if (currentParams.createTimeEnd) exportParam.createTimeEnd = currentParams.createTimeEnd

      if (currentParams.maintainerIds && currentParams.maintainerIds.length > 0)
        exportParam.maintainerIds = currentParams.maintainerIds as number[]

      if (currentParams.assemblyIds && currentParams.assemblyIds.length > 0)
        exportParam.assemblyIds = currentParams.assemblyIds as number[]

      if (currentParams.status !== undefined && currentParams.status !== null)
        exportParam.status = Number(currentParams.status)

      if (currentParams.electronicState !== undefined && currentParams.electronicState !== null)
        exportParam.electronicState = Boolean(currentParams.electronicState)

      if (currentParams.productName) exportParam.productName = currentParams.productName

      if (currentParams.productCodes && currentParams.productCodes.length > 0)
        exportParam.productCodes = currentParams.productCodes as string[]

      if (currentParams.productCode) exportParam.productCode = currentParams.productCode

      if (currentParams.skuCodes && currentParams.skuCodes.length > 0)
        exportParam.skuCodes = currentParams.skuCodes as string[]

      if (currentParams.skuCode) exportParam.skuCode = currentParams.skuCode

      if (currentParams.materialIds && currentParams.materialIds.length > 0)
        exportParam.materialIds = currentParams.materialIds as number[]

      // 清理空值
      Object.keys(exportParam).forEach((key) => {
        const value = exportParam[key as keyof ProductExportParam]
        if (
          value === undefined ||
          String(value) === '' ||
          (Array.isArray(value) && value.length === 0)
        ) {
          delete exportParam[key as keyof ProductExportParam]
        }
      })

      exportRequest.conditionalExportParam = exportParam
    }

    // 调用导出任务API
    const exportTaskReq: ExportTaskReq = {
      exportBizType: ExportTaskReqExportBizTypeEnum.PRODUCT_EXPORT,
      exportParams: JSON.stringify(exportRequest),
    }

    const result = await exportTaskApi.create({ exportTaskReq })

    if (result.data.success) {
      ElMessageBox.confirm('导出任务已创建成功，是否前往下载中心查看？', '导出成功', {
        confirmButtonText: '前往下载中心',
        cancelButtonText: '留在当前页面',
        type: 'success',
      })
        .then(() => {
          router.push('/download-center')
        })
        .catch(() => {
          // 用户选择留在当前页面，不做任何操作
        })
      return true
    }
    return false
  } catch (error) {
    console.error('导出产品时发生错误:', error)
    return false
  }
}

type TabStatus = 'all' | 'draft' | 'pending' | 'inStock' | 'notInStock'

watch(activeTab, () => {
  const statusMap: Record<TabStatus, number | undefined> = {
    all: undefined,
    draft: 0,
    pending: 1,
    inStock: 3,
    notInStock: 4,
  }

  queryModel.value.status = statusMap[activeTab.value as TabStatus]

  nextTick(() => {
    pageListRef.value?.reload()
  })
})

const sizeComponents = reactive([
  {
    type: 'input',
    label: '长',
    key: 'length',
    span: 8,
    component: markRaw(CgInputNumber),
    props: {
      type: 'number',
      placeholder: '长',
      min: 0,
      clearable: false,
    },
  },
  {
    type: 'input',
    label: '宽',
    key: 'width',
    span: 8,
    component: markRaw(CgInputNumber),
    props: {
      type: 'number',
      placeholder: '宽',
      min: 0,
      clearable: false,
    },
  },
  {
    type: 'input',
    label: '高',
    key: 'height',
    span: 8,
    component: markRaw(CgInputNumber),
    props: {
      type: 'number',
      placeholder: '高',
      min: 0,
      clearable: false,
    },
  },
])
</script>

<template>
  <div class="h-full flex flex-col">
    <PropertiesDialog
      ref="propertiesDialogRef"
      v-model="selectedProperties"
      :dialog-only="true"
      @change="handlePropertiesChange"
    />
    <div class="relative flex justify-between bg-white">
      <ElTabs v-model="activeTab" class="w-full">
        <ElTabPane v-for="tab in tabs" :key="tab.value" :label="tab.label" :name="tab.value" />
      </ElTabs>
      <!-- <div class="absolute right-0 top-0 mr-20px mt-2px flex flex-col justify-end">
        <ElRadioGroup v-model="viewMode">
          <ElRadioButton label="spu">
            SPU视图
          </ElRadioButton>
          <ElRadioButton label="sku">
            SKU视图
          </ElRadioButton>
        </ElRadioGroup>
      </div> -->
    </div>
    <CgPageList ref="pageListRef" v-model:query-model="queryModel" v-bind="gridOptions">
      <template #contentTemp="{ row }">
        <CgGrid v-bind="row.childGridOptions">
          <template #childPropertiesTemp="{ row: subSkuRow }">
            <ProductPropertiesDisplay :properties="subSkuRow.properties" />
          </template>
          <template #packageSizeTemp="{ row: subSkuRow }">
            <div v-for="(size, index) in subSkuRow.specs" :key="index">
              <CgComplexInputV2
                :model-value="{
                  length: size.length ? size.length / 10 : undefined,
                  width: size.width ? size.width / 10 : undefined,
                  height: size.height ? size.height / 10 : undefined,
                }"
                :components="sizeComponents"
                append="cm"
                :is-editing="false"
              />
            </div>
          </template>
        </CgGrid>
      </template>
      <template #propertiesTemp="{ row }">
        <ProductPropertiesDisplay :properties="row.properties" />
      </template>
      <template #categoriesTemp="{ row }">
        <span v-for="(category, index) in row.categories" :key="category.id">
          {{ category.categoryName }}<span v-if="index < row.categories.length - 1">></span>
        </span>
      </template>
    </CgPageList>
    <CgOperateLog
      v-if="operateLogVisible"
      :id="operateLogId"
      v-model="operateLogVisible"
      module="PRODUCT"
    />
    <!-- 工单优先级选择对话框 -->
    <PriorityDialog
      v-if="priorityDialogVisible"
      v-model:visible="priorityDialogVisible"
      @confirm="handlePriorityConfirm"
    />
    <!-- 工单创建结果对话框 -->
    <TicketCreateResultDialog
      v-if="resultDialogVisible"
      v-model:visible="resultDialogVisible"
      :result="ticketResultDetail"
      :product-code-map="productCodeMap"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__nav) {
  padding-left: 20px;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  // border: 0;
}

:deep(.el-radio-button.is-active) {
  .el-radio-button__inner {
    background-color: #ffffff !important;
    color: $cg-color-primary !important;
  }
}
</style>
