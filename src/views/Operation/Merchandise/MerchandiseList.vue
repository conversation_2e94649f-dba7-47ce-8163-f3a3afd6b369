<script setup lang="ts">
import type { ExportTaskReq } from '@/apiv2/file'
import type { CommodityListVO, CommoditySearchParam } from '@/apiv2/product'
import type { PageListInstance, PageListProps } from '@/components/CgPageList'
import type { VxeGlobalRendererHandles } from 'vxe-table'
import { exportTaskApi } from '@/api.services/file.service'
import { commodityApi, dictApi, supplierApi } from '@/api.services/product.service'
import { ExportTaskReqExportBizTypeEnum } from '@/apiv2/file'
import ComponentTagName from '@/common/component-tag-name'
import CgCategoryTree from '@/components/CgCategoryTree'
import { GridCellRenderName } from '@/components/CgGrid'
import { transformDictData } from '@/components/utils'
import { BUTTON_PERMISSION } from '@/enums/permission'
import { ProductOperationEnum } from '@/enums/product'
import router from '@/router'
import { useCacheStore } from '@/stores/Cache'
import { usePermissionStore } from '@/stores/Permission'
import { formatNumber } from '@/utils/number'
import XEUtils from 'xe-utils'
import DetailModal from './components/DetailModal.vue'
import PlatformStatusModal from './components/PlatformStatusModal.vue'
import PriceSettingModal from './components/PriceSettingModal.vue'
import SelectionStatusModal from './components/SelectionStatusModal.vue'
import { useModalWithSelection } from './composables/useModalWithSelection'

defineOptions({
  name: 'MerchandiseList',
})

// 商品导出请求参数接口
interface CommodityExportRequest {
  conditionalExportParam?: CommodityExportParam
  exportCommodityIds?: number[]
}

// 商品导出条件参数接口
interface CommodityExportParam {
  locale?: string
  categoryIds?: number[]
  productCodes?: string[]
  productCode?: string
  commodityName?: string
  productStatus?: number
  selectionStatus?: number
  createTimeStart?: string
  createTimeEnd?: string
  styles?: number[]
  platformIds?: number[]
  listingStatuses?: number[]
}

const pageListRef = ref<PageListInstance | null>(null)
const queryModel = ref<Partial<CommoditySearchParam>>({
  locale: undefined,
  categoryIds: undefined,
  // 批量精确搜索
  productCodes: [],
  // 模糊搜索
  productCode: undefined,
  // 模糊搜索
  commodityName: undefined,
  productStatus: undefined,
  page: 1,
  size: 20,
  selectionStatus: undefined,
  createTimeStart: undefined,
  createTimeEnd: undefined,
  styles: undefined,
  platformIds: undefined,
  listingStatuses: undefined,
})

const cacheStore = useCacheStore()

// 引入按钮鉴权
const { hasButtonPermission } = usePermissionStore()

// 检查是否有查看商品详情权限
const hasDetailPermission = computed(() => {
  return hasButtonPermission(BUTTON_PERMISSION.COMMODITY_DETAIL)
})

// 按钮禁用状态样式
const btnDisabledClass = computed(() => {
  return hasDetailPermission.value
    ? []
    : ['text-gray-400', 'cursor-not-allowed']
})

const platformNameMap = computed(() => {
  const map = new Map<string, string>()
  if (cacheStore.platformList && cacheStore.platformList.length > 0) {
    cacheStore.platformList.forEach((p) => {
      if (p.id !== undefined && p.platformName) {
        map.set(String(p.id), p.platformName)
      }
    })
  }
  return map
})

function getPlatformName(platformIdentifier: string | number | undefined): string {
  if (!platformIdentifier) {
    return '未知平台'
  }
  return platformNameMap.value.get(String(platformIdentifier)) || String(platformIdentifier)
}

function handleSpuClick(row: CommodityListVO) {
  // 模拟禁用行为
  if (!hasDetailPermission.value)
    return

  router.push(`/product/detail/${row.productId}?isOperation=1&commodityId=${row.id}`)
}

function operatorCmd(
  params: VxeGlobalRendererHandles.RenderTableCellParams,
  cmd: { id: string, text: string },
) {
  const row = params.row
  switch (cmd.id) {
    case ProductOperationEnum.EDIT:
      router.push(`/product/edit/${row.productId}?isOperation=1&commodityId=${row.id}`)
      break
  }
}

const modalSelectionStatusVisible = ref(false)
const modalPlatformStatusVisible = ref(false)
const modalPriceVisible = ref(false)
const modalDetailVisible = ref(false)
const currentMerchandiseId = ref<number>()
const selectedMerchandiseData = ref<CommodityListVO[]>([]) // 存储选中的完整行数据

function refreshList() {
  pageListRef.value?.reload()
}

const { selectedIds, selectedCount, createModalWatcher } = useModalWithSelection(pageListRef)

const gridOptions = reactive<PageListProps>({
  gridOption: {
    options: {
      columns: [
        {
          field: 'id',
          type: 'checkbox',
          width: 36,
          fixed: 'left',
          resizable: false,
        },
        {
          field: 'productInfo',
          title: '产品信息',
          minWidth: 300,
          slots: {
            default: 'productInfoTemp',
          },
        },
        {
          field: 'otherProperties',
          title: '其他属性',
          width: 120,
          slots: {
            default: 'otherPropertiesTemp',
          },
        },
        {
          field: 'supplierInfo',
          title: '供应商信息',
          width: 120,
          slots: {
            default: 'supplierInfoTemp',
          },
        },
        {
          field: 'price',
          title: '成本价',
          width: 120,
          slots: {
            default: 'priceTemp',
          },
        },
        {
          field: 'marketPrice',
          title: '市场售价',
          width: 120,
          slots: {
            default: 'marketPriceTemp',
          },
        },
        {
          field: 'productStatus',
          title: '产品状态',
          width: 88,
          slots: {
            default: 'statusTemp',
          },
        },
        {
          field: 'selectionStatus',
          title: '选品状态',
          width: 120,
          slots: {
            default: 'selectStatusTemp',
          },
        },
        // {
        //   field: 'localizationStatus',
        //   title: '本地化状态',
        //   width: 200,
        //   slots: {
        //     default: 'localizationStatusTemp',
        //   },
        // },
        {
          field: 'platformStatus',
          title: '平台商品状态',
          width: 180,
          slots: {
            default: 'platformStatusTemp',
          },
        },
        {
          field: 'time',
          title: '时间',
          width: 180,
          slots: {
            default: 'timeTemp',
          },
        },
        {
          field: 'operator',
          title: '操作',
          width: 80,
          align: 'center',
          headerAlign: 'center',
          fixed: 'right',
          cellRender: {
            name: GridCellRenderName.Operate,
            props: {
              items: [
                {
                  text: '编辑',
                  id: ProductOperationEnum.EDIT,
                  disabled: !hasButtonPermission(BUTTON_PERMISSION.COMMODITY_EDIT),
                },
              ],
            },
            events: { command: operatorCmd },
          },
        },
      ],
      round: true,
      showOverflow: false,
      // cellConfig: {
      //   padding: false,
      // },
    },
    toolbarConfig: {
      exportFile: {
        exportProxy: {
          request: async (params) => {
            return handleExportCommodities(params)
          },
        },
        disabled: !hasButtonPermission(BUTTON_PERMISSION.COMMODITY_EXPORT),
        // teleport: '.cg-page-header__right',
      },
      refresh: true,
      buttons: [
        {
          text: '设置选品状态',
          on: {
            click: () => {
              modalSelectionStatusVisible.value = true
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.COMMODITY_SELECTION_STATUS_UPDATE),
          },
        },
        {
          text: '设置商品状态',
          on: {
            click: () => {
              modalPlatformStatusVisible.value = true
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.COMMODITY_PLATFORM_STATUS_UPDATE),
          },
        },
        {
          text: '设置售价',
          on: {
            click: () => {
              // 手动获取选中数据并打开模态框
              const selectedRows = pageListRef.value?.getCheckboxRecords() || []
              if (selectedRows.length === 0) {
                ElMessage.warning('请至少选择一个商品进行售价设置')
                return
              }
              // 检查选中数据是否包含必要的 skus 信息
              const hasInvalidData = selectedRows.some(row => !row.skus || row.skus.length === 0)
              if (hasInvalidData) {
                ElMessage.warning('部分选中商品缺少 SKU 信息，无法进行售价设置。')
                // 如果需要过滤掉无效数据，可以取消下面这行注释
                // selectedMerchandiseData.value = selectedRows.filter(row => row.skus && row.skus.length > 0);
                // return; // 如果严格要求所有选中项都必须有 SKU，则阻止打开
              }

              selectedMerchandiseData.value = selectedRows // 存储完整数据
              modalPriceVisible.value = true // 打开模态框
            },
          },
          props: {
            disabled: !hasButtonPermission(BUTTON_PERMISSION.COMMODITY_SKU_PRICES_UPDATE),
          },
        },
        // {
        //   text: '导出刊登数据',
        //   on: {
        //     click: () => {
        //       // TODO
        //     },
        //   },
        // },
      ],
    },
    proxyOption: {
      request: commodityApi.listCommodities,
      postParam: 'commoditySearchParam',
    },
  },
  maxQueryFormVisibleCount: 8,
  searchMoreDisplayMode: 'icon',
  queryForm: [
    {
      component: ComponentTagName.Input,
      vmodel: 'productCode',
      props: {
        placeholder: 'SPU',
        class: 'min-w-30',
      },
    },
    {
      component: ComponentTagName.Input,
      vmodel: 'commodityName',
      props: {
        placeholder: '商品品名',
        class: 'min-w-30',
      },
    },
    {
      component: markRaw(CgCategoryTree),
      vmodel: 'categoryIds',
      props: {
        placeholder: '类目',
        showCheckbox: true,
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'productStatus',
      props: {
        data: [
          { label: '已上架', value: 3 },
          { label: '已下架', value: 4 },
        ],
        placeholder: '产品状态',
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'selectionStatus',
      props: {
        data: [
          { label: '待审核', value: 0 },
          { label: '通过', value: 1 },
          { label: '不通过', value: 2 },
        ],
        placeholder: '选品状态',
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'platformIds',
      props: {
        placeholder: '平台',
        proxyOption: {
          // 使用代理获取平台列表
          request: commodityApi.listPlatforms,
        },
        filterable: true,
        multiple: true,
        props: {
          label: 'platformName',
          value: 'id',
        },
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'listingStatuses',
      props: {
        data: [
          { label: '待启动', value: 0 },
          { label: '准备中', value: 1 },
          { label: '已上架', value: 2 },
          { label: '已下架', value: 3 },
        ],
        placeholder: '商品状态',
        multiple: true,
      },
    },
    {
      component: ComponentTagName.ComplexInput,
      vmodel: {
        modelValue: 'date',
        modelSelect: 'searchTimeType',
        start: 'createTimeStart',
        end: 'createTimeEnd',
      },
      props: {
        type: 'date',
        clearable: false,
        defaultSelectRange: { type: 'month', rang: -1 },
        selectOptions: [
          { value: 1, label: '创建时间' },
          { value: 2, label: '更新时间' },
        ],
      },
    },
    // {
    //   component: ComponentTagName.SelectV2,
    //   vmodel: 'electronicState',
    //   label: '是否带电',
    //   props: {
    //     data: [
    //       { label: '是', value: true },
    //       { label: '否', value: false },
    //     ],
    //     placeholder: '请选择',
    //   },
    // },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'assemblyIds',
      label: '组装',
      props: {
        placeholder: '请选择是否组装',
        proxyOption: {
          request: dictApi.listDicts,
          query: {
            codes: 'assembly',
          },
        },
        transform: transformDictData,
        filterable: false,
      },
    },
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'supplierIds',
      label: '供应商',
      props: {
        placeholder: '请选择',
        proxyOption: {
          request: supplierApi.listSuppliers,
        },
        filterable: true,
        multiple: true,
        props: {
          label: 'supplierName',
          value: 'id',
        },
      },
    },
  ],
  autoLoad: true,
})

// 为每个模态框创建监听器
createModalWatcher(modalSelectionStatusVisible)
createModalWatcher(modalPlatformStatusVisible)

/**
 * 处理商品导出到ShopLine
 * 改进版本，提供更好的错误处理和用户体验
 */
async function handleExportCommodities(_params: any) {
  try {
    // 获取选中的商品记录
    const selectedRecords = pageListRef.value?.getCheckboxRecords() || []

    // 构建导出请求参数
    const exportRequest: CommodityExportRequest = {}

    // 如果有选中的商品，则使用选中的ID进行导出
    if (selectedRecords.length > 0) {
      // 过滤掉可能的 undefined 值并确保 ID 是数字类型
      exportRequest.exportCommodityIds = selectedRecords
        .map((record: CommodityListVO) => record.id)
        .filter((id): id is number => id !== undefined)

      if (exportRequest.exportCommodityIds.length === 0) {
        ElMessage.warning('选中的商品中没有有效的ID，无法导出')
        return false
      }
    }
    // 否则使用当前的查询条件进行导出
    else {
      const currentParams = queryModel.value
      const exportParam: CommodityExportParam = {}

      // 安全地添加参数，避免类型错误
      if (currentParams.locale) {
        exportParam.locale = currentParams.locale
      }

      if (currentParams.categoryIds && currentParams.categoryIds.length > 0) {
        exportParam.categoryIds = currentParams.categoryIds.filter((id): id is number => id !== null)
      }

      if (currentParams.productCodes && currentParams.productCodes.length > 0) {
        exportParam.productCodes = currentParams.productCodes
      }

      if (currentParams.productCode) {
        exportParam.productCode = currentParams.productCode
      }

      if (currentParams.commodityName) {
        exportParam.commodityName = currentParams.commodityName
      }

      if (currentParams.productStatus !== undefined) {
        exportParam.productStatus = currentParams.productStatus
      }

      if (currentParams.selectionStatus !== undefined) {
        exportParam.selectionStatus = currentParams.selectionStatus
      }

      if (currentParams.createTimeStart) {
        exportParam.createTimeStart = currentParams.createTimeStart
      }

      if (currentParams.createTimeEnd) {
        exportParam.createTimeEnd = currentParams.createTimeEnd
      }

      if (currentParams.styles && currentParams.styles.length > 0) {
        exportParam.styles = currentParams.styles
      }

      if (currentParams.platformIds && currentParams.platformIds.length > 0) {
        exportParam.platformIds = currentParams.platformIds
      }

      if (currentParams.listingStatuses && currentParams.listingStatuses.length > 0) {
        exportParam.listingStatuses = currentParams.listingStatuses
      }

      // 清理空值
      Object.keys(exportParam).forEach((key) => {
        const value = exportParam[key as keyof CommodityExportParam]
        if (
          value === undefined
          || String(value) === ''
          || (Array.isArray(value) && value.length === 0)
        ) {
          delete exportParam[key as keyof CommodityExportParam]
        }
      })

      exportRequest.conditionalExportParam = exportParam
    }

    // 调用导出任务API
    const exportTaskReq: ExportTaskReq = {
      exportBizType: ExportTaskReqExportBizTypeEnum.COMMODITY_SHOP_LINE_EXPORT,
      exportParams: JSON.stringify(exportRequest),
    }

    const result = await exportTaskApi.create({ exportTaskReq })

    if (result.data.success) {
      ElMessageBox.confirm('导出任务已创建成功，是否前往下载中心查看？', '导出成功', {
        confirmButtonText: '前往下载中心',
        cancelButtonText: '留在当前页面',
        type: 'success',
      })
        .then(() => {
          router.push('/download-center')
        })
        .catch(() => {
          // 用户选择留在当前页面，不做任何操作
        })
      return true
    }
    else {
      ElMessage.error(result.data.message || '导出任务创建失败')
      return false
    }
  }
  catch (error) {
    console.error('导出商品时发生错误:', error)
    ElMessage.error('导出失败，请稍后重试')
    return false
  }
}

const currentRowData = ref<CommodityListVO | undefined>(undefined)
function handleDetailClick(row: CommodityListVO) {
  // 模拟禁用行为
  if (!hasDetailPermission.value)
    return

  currentMerchandiseId.value = row.id
  currentRowData.value = row
  modalDetailVisible.value = true
}
</script>

<template>
  <div class="h-full flex flex-col">
    <CgPageList ref="pageListRef" v-model:query-model="queryModel" v-bind="gridOptions">
      <template #productInfoTemp="{ row }">
        <div class="flex items-center p-1">
          <div
            class="mr-2.5 h-20 w-20 flex-shrink-0 overflow-hidden border border-gray-200 rounded"
          >
            <el-image
              :src="row.thumbnail"
              fit="cover"
              alt="商品图片"
              class="h-full w-full object-cover transition-transform duration-300 hover:scale-110"
              :hide-on-click-modal="true"
              lazy
            />
          </div>
          <div class="flex flex-col justify-between">
            <div class="relative flex items-center leading-tight">
              <span class="mr-1.5 text-12px text-gray-500"> SPU: </span>
              <a
                class="flex cursor-pointer items-center text-12px text-#399e96 font-medium tracking-wide transition-colors duration-200 hover:text-#399e96/80 hover:underline"
                :class="btnDisabledClass"
                @click="handleSpuClick(row)"
              >
                {{ row.productCode || '-' }}
              </a>
            </div>
            <p class="m-0 leading-tight">
              <span class="mr-1 text-gray-500">款名:</span>
              <span class="text-gray-800">{{ row.productName || '-' }}</span>
            </p>
            <p class="m-0 leading-tight">
              <span class="mr-1 text-gray-500">商品品名:</span>
              <span class="text-gray-800">{{ row.commodityName || '-' }}</span>
            </p>
            <p class="m-0 leading-tight">
              <span class="mr-1 text-gray-500">类目:</span>
              <span class="text-gray-800">{{
                row.categories?.map((cat: any) => cat.categoryName).join('>') || '-'
              }}</span>
            </p>
          </div>
        </div>
      </template>
      <template #otherPropertiesTemp="{ row }">
        <div class="p-1">
          <p class="m-0 leading-tight">
            <span class="mr-1 text-gray-500">组装:</span>
            <span class="text-gray-800">{{ row.assembly?.valueName || '-' }}</span>
          </p>
          <p class="m-0 leading-tight">
            <span class="mr-1 text-gray-500">说明书:</span>
            <span class="text-gray-800">{{ row.instrumentState?.valueName || '-' }}</span>
          </p>
        </div>
      </template>
      <template #supplierInfoTemp="{ row }">
        <div class="p-1">
          <p class="m-0 leading-tight">
            <span class="mr-1 text-gray-500">供应商:</span>
            <span class="text-gray-800">{{ row.supplier?.supplierName || '-' }}</span>
          </p>
          <p class="m-0 leading-tight">
            <span class="mr-1 text-gray-500">等级:</span>
            <span class="text-gray-800">{{ row.supplier?.supplierLevel || '-' }}</span>
          </p>
        </div>
      </template>
      <template #priceTemp="{ row }">
        <div class="p-1">
          <p class="m-0 font-medium lh-12px">
            <span class="text-gray-500">JPY: </span><span class="text-gray-800">{{ formatNumber(row.supplyPrice?.min || '0', 0) }}</span>
          </p>
          <p class="m-0 lh-12px">
            ~
          </p>
          <p class="m-0 font-medium lh-12px">
            <span class="text-gray-500">JPY: </span><span class="text-gray-800">{{ formatNumber(row.supplyPrice?.max || '0', 0) }}</span>
          </p>
        </div>
      </template>
      <template #marketPriceTemp="{ row }">
        <div class="p-1">
          <p class="m-0 font-medium lh-12px">
            <span class="text-gray-500">JPY: </span><span class="text-gray-800">{{ formatNumber(row.marketPrice?.min || '0', 0) }}</span>
          </p>
          <p class="m-0 lh-12px">
            ~
          </p>
          <p class="m-0 font-medium lh-12px">
            <span class="text-gray-500">JPY: </span><span class="text-gray-800">{{ formatNumber(row.marketPrice?.max || '0', 0) }}</span>
          </p>
          <p class="m-0 cursor-pointer text-blue-500" :class="btnDisabledClass" @click="handleDetailClick(row)">
            <span>查看详情</span>
          </p>
        </div>
      </template>
      <template #statusTemp="{ row }">
        <div class="p-1">
          <p class="m-0">
            <ElTag
              disable-transitions
              :type="
                row.productStatus === 3
                  ? 'success'
                  : row.productStatus === 4
                    ? 'danger'
                    : row.productStatus === 0
                      ? 'info'
                      : 'warning'
              "
              size="small"
              effect="light"
            >
              {{
                row.productStatus === 0
                  ? '草稿'
                  : row.productStatus === 1
                    ? '待审核'
                    : row.productStatus === 2
                      ? '已审核'
                      : row.productStatus === 3
                        ? '已上架'
                        : row.productStatus === 4
                          ? '已下架'
                          : row.productStatus === 5
                            ? '已删除'
                            : '未知'
              }}
            </ElTag>
          </p>
          <p class="m-0 cursor-pointer text-blue-500" :class="btnDisabledClass" @click="handleDetailClick(row)">
            <span>查看详情</span>
          </p>
        </div>
      </template>
      <template #selectStatusTemp="{ row }">
        <div class="p-1">
          <p class="m-0">
            <ElTag
              disable-transitions
              :type="
                row.selectionStatus === 1
                  ? 'success'
                  : row.selectionStatus === 2
                    ? 'danger'
                    : 'info'
              "
              size="small"
              effect="light"
            >
              {{
                row.selectionStatus === 0
                  ? '待审核'
                  : row.selectionStatus === 1
                    ? '通过'
                    : row.selectionStatus === 2
                      ? '不通过'
                      : '未知'
              }}
            </ElTag>
          </p>
          <p v-if="row.selectionReason" class="m-0 mt-1">
            <span class="line-clamp-3 text-gray-500" :title="row.selectionReason">{{
              row.selectionReason
            }}</span>
          </p>
        </div>
      </template>
      <template #localizationStatusTemp>
        <div class="p-1">
          <p class="m-0 text-gray-600 leading-tight">
            <!-- TODO: 接口缺少本地化状态信息 -->
            本地化状态信息
          </p>
        </div>
      </template>
      <template #platformStatusTemp="{ row }">
        <div class="p-1">
          <template v-if="row.commodityPlatforms && row.commodityPlatforms.length > 0">
            <div
              v-for="(platformItem, index) in row.commodityPlatforms.slice(0, 3)"
              :key="index"
              class="m-0 mb-0.5 flex items-center"
            >
              <span
                class="mr-1 inline-block min-w-10 truncate text-gray-500"
                :title="getPlatformName(platformItem.platform)"
              >{{ getPlatformName(platformItem.platform) }}:</span>
              <ElTag
                disable-transitions
                size="small"
                effect="light"
                :type="
                  platformItem.listingStatus === 2
                    ? 'success'
                    : platformItem.listingStatus === 3
                      ? 'danger'
                      : 'info'
                "
              >
                {{
                  platformItem.listingStatus === 0
                    ? '待启动'
                    : platformItem.listingStatus === 1
                      ? '准备中'
                      : platformItem.listingStatus === 2
                        ? '已上架'
                        : platformItem.listingStatus === 3
                          ? '已下架'
                          : '未知'
                }}
              </ElTag>
            </div>
            <!-- 如果超过 3 个，显示提示 -->
            <el-tooltip
              v-if="row.commodityPlatforms.length > 3"
              placement="bottom-start"
              effect="light"
            >
              <template #content>
                <div
                  v-for="(platformItem, index) in row.commodityPlatforms.slice(3)"
                  :key="index"
                  class="mb-0.5 flex items-center text-xs"
                >
                  <span
                    class="mr-1 inline-block max-w-14 min-w-10 truncate text-gray-500"
                    :title="getPlatformName(platformItem.platform)"
                  >{{ getPlatformName(platformItem.platform) }}:</span>
                  <ElTag
                    disable-transitions
                    size="small"
                    effect="light"
                    :type="
                      platformItem.listingStatus === 2
                        ? 'success'
                        : platformItem.listingStatus === 3
                          ? 'danger'
                          : 'info'
                    "
                  >
                    {{
                      platformItem.listingStatus === 0
                        ? '待启动'
                        : platformItem.listingStatus === 1
                          ? '准备中'
                          : platformItem.listingStatus === 2
                            ? '已上架'
                            : platformItem.listingStatus === 3
                              ? '已下架'
                              : '未知'
                    }}
                  </ElTag>
                </div>
              </template>
              <span class="cursor-pointer text-xs text-gray-400 hover:text-gray-600">...更多</span>
            </el-tooltip>
          </template>
          <p v-else class="m-0 leading-tight">
            <span class="text-gray-500">暂无平台信息</span>
          </p>
        </div>
      </template>
      <template #timeTemp="{ row }">
        <div class="p-1">
          <p class="m-0 leading-tight">
            <span class="mr-1 text-gray-500">创建时间:</span>
            <span class="text-gray-800">{{
              row.createTime ? XEUtils.toDateString(row.createTime, 'yyyy-MM-dd HH:mm:ss') : '-'
            }}</span>
          </p>
          <p class="m-0 leading-tight">
            <span class="mr-1 text-gray-500">更新时间:</span>
            <span class="text-gray-800">{{
              row.updateTime ? XEUtils.toDateString(row.updateTime, 'yyyy-MM-dd HH:mm:ss') : '-'
            }}</span>
          </p>
        </div>
      </template>
    </CgPageList>
    <!-- 模态框 -->
    <SelectionStatusModal
      v-if="modalSelectionStatusVisible"
      v-model="modalSelectionStatusVisible"
      :selected-count="selectedCount"
      :selected-ids="selectedIds"
      @success="refreshList"
    />
    <PlatformStatusModal
      v-if="modalPlatformStatusVisible"
      v-model="modalPlatformStatusVisible"
      :selected-count="selectedCount"
      :selected-ids="selectedIds"
      @success="refreshList"
    />
    <PriceSettingModal
      v-if="modalPriceVisible"
      v-model="modalPriceVisible"
      :selected-items="selectedMerchandiseData"
      @success="refreshList"
    />
    <DetailModal
      v-if="modalDetailVisible"
      v-model="modalDetailVisible"
      :merchandise-id="currentMerchandiseId"
      :row-data="currentRowData"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__nav) {
  padding-left: 20px;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  // border: 0;
}

:deep(.el-radio-button.is-active) {
  .el-radio-button__inner {
    background-color: #ffffff !important;
    color: $cg-color-primary !important;
  }
}
</style>
