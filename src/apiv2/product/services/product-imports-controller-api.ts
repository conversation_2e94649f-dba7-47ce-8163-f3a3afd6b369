/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { MetaFieldImportsRequest } from '../models';
// @ts-ignore
import type { ResultVOListMetaFieldImportsVO } from '../models';
/**
 * ProductImportsControllerApi - axios parameter creator
 * @export
 */
export const ProductImportsControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 导入元数据
         * @param {MetaFieldImportsRequest} metaFieldImportsRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsMetaField: async (metaFieldImportsRequest: MetaFieldImportsRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'metaFieldImportsRequest' is not null or undefined
            assertParamExists('importsMetaField', 'metaFieldImportsRequest', metaFieldImportsRequest)
            const localVarPath = `/api/products/imports/meta-field`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(metaFieldImportsRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProductImportsControllerApi - functional programming interface
 * @export
 */
export const ProductImportsControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProductImportsControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 导入元数据
         * @param {MetaFieldImportsRequest} metaFieldImportsRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importsMetaField(metaFieldImportsRequest: MetaFieldImportsRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListMetaFieldImportsVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importsMetaField(metaFieldImportsRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductImportsControllerApi.importsMetaField']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProductImportsControllerApi - factory interface
 * @export
 */
export const ProductImportsControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProductImportsControllerApiFp(configuration)
    return {
        /**
         * 
         * @summary 导入元数据
         * @param {ProductImportsControllerApiImportsMetaFieldRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsMetaField(requestParameters: ProductImportsControllerApiImportsMetaFieldRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListMetaFieldImportsVO> {
            return localVarFp.importsMetaField(requestParameters.metaFieldImportsRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for importsMetaField operation in ProductImportsControllerApi.
 * @export
 * @interface ProductImportsControllerApiImportsMetaFieldRequest
 */
export interface ProductImportsControllerApiImportsMetaFieldRequest {
    /**
     * 
     * @type {MetaFieldImportsRequest}
     * @memberof ProductImportsControllerApiImportsMetaField
     */
    readonly metaFieldImportsRequest: MetaFieldImportsRequest
}

/**
 * ProductImportsControllerApi - object-oriented interface
 * @export
 * @class ProductImportsControllerApi
 * @extends {BaseAPI}
 */
export class ProductImportsControllerApi extends BaseAPI {
    /**
     * 
     * @summary 导入元数据
     * @param {ProductImportsControllerApiImportsMetaFieldRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductImportsControllerApi
     */
    public importsMetaField(requestParameters: ProductImportsControllerApiImportsMetaFieldRequest, options?: RawAxiosRequestConfig) {
        return ProductImportsControllerApiFp(this.configuration).importsMetaField(requestParameters.metaFieldImportsRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

