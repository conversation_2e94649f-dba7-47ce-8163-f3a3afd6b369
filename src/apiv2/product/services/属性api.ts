/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOListPropertyVO } from '../models';
// @ts-ignore
import type { ResultVOListShoplineExportMappingDTO } from '../models';
// @ts-ignore
import type { ResultVOListString } from '../models';
/**
 * 属性Api - axios parameter creator
 * @export
 */
export const 属性ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 属性列表
         * @param {Array<string>} [codes] 属性code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProperties: async (codes?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/properties`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (codes) {
                localVarQueryParameter['codes'] = codes;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 属性code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPropertyCodes: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/properties/codes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryPropertyMapping: async (requestBody: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('queryPropertyMapping', 'requestBody', requestBody)
            const localVarPath = `/api/properties/queryShopLinePropertyMapping`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 属性Api - functional programming interface
 * @export
 */
export const 属性ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 属性ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 属性列表
         * @param {Array<string>} [codes] 属性code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listProperties(codes?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListPropertyVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listProperties(codes, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['属性Api.listProperties']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 属性code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listPropertyCodes(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listPropertyCodes(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['属性Api.listPropertyCodes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryPropertyMapping(requestBody: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListShoplineExportMappingDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryPropertyMapping(requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['属性Api.queryPropertyMapping']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 属性Api - factory interface
 * @export
 */
export const 属性ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 属性ApiFp(configuration)
    return {
        /**
         * 
         * @summary 属性列表
         * @param {属性ApiListPropertiesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listProperties(requestParameters: 属性ApiListPropertiesRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListPropertyVO> {
            return localVarFp.listProperties(requestParameters.codes, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 属性code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPropertyCodes(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListString> {
            return localVarFp.listPropertyCodes(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {属性ApiQueryPropertyMappingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryPropertyMapping(requestParameters: 属性ApiQueryPropertyMappingRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListShoplineExportMappingDTO> {
            return localVarFp.queryPropertyMapping(requestParameters.requestBody, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for listProperties operation in 属性Api.
 * @export
 * @interface 属性ApiListPropertiesRequest
 */
export interface 属性ApiListPropertiesRequest {
    /**
     * 属性code列表
     * @type {Array<string>}
     * @memberof 属性ApiListProperties
     */
    readonly codes?: Array<string>
}

/**
 * Request parameters for queryPropertyMapping operation in 属性Api.
 * @export
 * @interface 属性ApiQueryPropertyMappingRequest
 */
export interface 属性ApiQueryPropertyMappingRequest {
    /**
     * 
     * @type {Array<number>}
     * @memberof 属性ApiQueryPropertyMapping
     */
    readonly requestBody: Array<number>
}

/**
 * 属性Api - object-oriented interface
 * @export
 * @class 属性Api
 * @extends {BaseAPI}
 */
export class 属性Api extends BaseAPI {
    /**
     * 
     * @summary 属性列表
     * @param {属性ApiListPropertiesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 属性Api
     */
    public listProperties(requestParameters: 属性ApiListPropertiesRequest = {}, options?: RawAxiosRequestConfig) {
        return 属性ApiFp(this.configuration).listProperties(requestParameters.codes, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 属性code列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 属性Api
     */
    public listPropertyCodes(options?: RawAxiosRequestConfig) {
        return 属性ApiFp(this.configuration).listPropertyCodes(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {属性ApiQueryPropertyMappingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 属性Api
     */
    public queryPropertyMapping(requestParameters: 属性ApiQueryPropertyMappingRequest, options?: RawAxiosRequestConfig) {
        return 属性ApiFp(this.configuration).queryPropertyMapping(requestParameters.requestBody, options).then((request) => request(this.axios, this.basePath));
    }
}

