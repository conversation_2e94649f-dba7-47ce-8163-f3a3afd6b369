/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { MetaFieldExportRequest } from '../models';
// @ts-ignore
import type { ResultVOListMetaFieldExcelVO } from '../models';
/**
 * ProductExportControllerApi - axios parameter creator
 * @export
 */
export const ProductExportControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 导出元数据
         * @param {MetaFieldExportRequest} metaFieldExportRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportMetaField: async (metaFieldExportRequest: MetaFieldExportRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'metaFieldExportRequest' is not null or undefined
            assertParamExists('exportMetaField', 'metaFieldExportRequest', metaFieldExportRequest)
            const localVarPath = `/api/products/export/meta-field`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(metaFieldExportRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProductExportControllerApi - functional programming interface
 * @export
 */
export const ProductExportControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProductExportControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 导出元数据
         * @param {MetaFieldExportRequest} metaFieldExportRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async exportMetaField(metaFieldExportRequest: MetaFieldExportRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListMetaFieldExcelVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.exportMetaField(metaFieldExportRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductExportControllerApi.exportMetaField']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProductExportControllerApi - factory interface
 * @export
 */
export const ProductExportControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProductExportControllerApiFp(configuration)
    return {
        /**
         * 
         * @summary 导出元数据
         * @param {ProductExportControllerApiExportMetaFieldRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportMetaField(requestParameters: ProductExportControllerApiExportMetaFieldRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListMetaFieldExcelVO> {
            return localVarFp.exportMetaField(requestParameters.metaFieldExportRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for exportMetaField operation in ProductExportControllerApi.
 * @export
 * @interface ProductExportControllerApiExportMetaFieldRequest
 */
export interface ProductExportControllerApiExportMetaFieldRequest {
    /**
     * 
     * @type {MetaFieldExportRequest}
     * @memberof ProductExportControllerApiExportMetaField
     */
    readonly metaFieldExportRequest: MetaFieldExportRequest
}

/**
 * ProductExportControllerApi - object-oriented interface
 * @export
 * @class ProductExportControllerApi
 * @extends {BaseAPI}
 */
export class ProductExportControllerApi extends BaseAPI {
    /**
     * 
     * @summary 导出元数据
     * @param {ProductExportControllerApiExportMetaFieldRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductExportControllerApi
     */
    public exportMetaField(requestParameters: ProductExportControllerApiExportMetaFieldRequest, options?: RawAxiosRequestConfig) {
        return ProductExportControllerApiFp(this.configuration).exportMetaField(requestParameters.metaFieldExportRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

