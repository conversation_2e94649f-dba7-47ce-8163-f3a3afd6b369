/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOCategoryImportsVO } from '../models';
/**
 * CategoryImportsControllerApi - axios parameter creator
 * @export
 */
export const CategoryImportsControllerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 批量导入类目
         * @param {string} taskCode 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsCategories: async (taskCode: string, file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskCode' is not null or undefined
            assertParamExists('importsCategories', 'taskCode', taskCode)
            // verify required parameter 'file' is not null or undefined
            assertParamExists('importsCategories', 'file', file)
            const localVarPath = `/api/categories/imports/`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            if (taskCode !== undefined) {
                localVarQueryParameter['taskCode'] = taskCode;
            }


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CategoryImportsControllerApi - functional programming interface
 * @export
 */
export const CategoryImportsControllerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CategoryImportsControllerApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 批量导入类目
         * @param {string} taskCode 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async importsCategories(taskCode: string, file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOCategoryImportsVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importsCategories(taskCode, file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CategoryImportsControllerApi.importsCategories']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CategoryImportsControllerApi - factory interface
 * @export
 */
export const CategoryImportsControllerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CategoryImportsControllerApiFp(configuration)
    return {
        /**
         * 
         * @summary 批量导入类目
         * @param {CategoryImportsControllerApiImportsCategoriesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        importsCategories(requestParameters: CategoryImportsControllerApiImportsCategoriesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOCategoryImportsVO> {
            return localVarFp.importsCategories(requestParameters.taskCode, requestParameters.file, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for importsCategories operation in CategoryImportsControllerApi.
 * @export
 * @interface CategoryImportsControllerApiImportsCategoriesRequest
 */
export interface CategoryImportsControllerApiImportsCategoriesRequest {
    /**
     * 
     * @type {string}
     * @memberof CategoryImportsControllerApiImportsCategories
     */
    readonly taskCode: string

    /**
     * 
     * @type {File}
     * @memberof CategoryImportsControllerApiImportsCategories
     */
    readonly file: File
}

/**
 * CategoryImportsControllerApi - object-oriented interface
 * @export
 * @class CategoryImportsControllerApi
 * @extends {BaseAPI}
 */
export class CategoryImportsControllerApi extends BaseAPI {
    /**
     * 
     * @summary 批量导入类目
     * @param {CategoryImportsControllerApiImportsCategoriesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CategoryImportsControllerApi
     */
    public importsCategories(requestParameters: CategoryImportsControllerApiImportsCategoriesRequest, options?: RawAxiosRequestConfig) {
        return CategoryImportsControllerApiFp(this.configuration).importsCategories(requestParameters.taskCode, requestParameters.file, options).then((request) => request(this.axios, this.basePath));
    }
}

