/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface CommodityFileDTO
 */
export interface CommodityFileDTO {
    /**
     * 
     * @type {number}
     * @memberof CommodityFileDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommodityFileDTO
     */
    'filename'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityFileDTO
     */
    'filePath'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityFileDTO
     */
    'fileUrl'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommodityFileDTO
     */
    'fileType'?: CommodityFileDTOFileTypeEnum;
    /**
     * 
     * @type {number}
     * @memberof CommodityFileDTO
     */
    'fileSize'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommodityFileDTO
     */
    'fileDesc'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommodityFileDTO
     */
    'sortOrder'?: number;
}

export const CommodityFileDTOFileTypeEnum = {
    ATTACHMENT: 0,
    INSTRUMENT: 1,
    CERTIFICATION: 2,
    THREE_D: 3,
    SEO_VIDEO: 4
} as const;

export type CommodityFileDTOFileTypeEnum = typeof CommodityFileDTOFileTypeEnum[keyof typeof CommodityFileDTOFileTypeEnum];


