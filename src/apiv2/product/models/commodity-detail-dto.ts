/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CommodityFileDTO } from './commodity-file-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { CommodityImageDTO } from './commodity-image-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { CommoditySkuDTO } from './commodity-sku-dto';

/**
 * 数据
 * @export
 * @interface CommodityDetailDTO
 */
export interface CommodityDetailDTO {
    /**
     * 
     * @type {number}
     * @memberof CommodityDetailDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommodityDetailDTO
     */
    'productId'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommodityDetailDTO
     */
    'productCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityDetailDTO
     */
    'supplierCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityDetailDTO
     */
    'commodityName'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommodityDetailDTO
     */
    'productStatus'?: CommodityDetailDTOProductStatusEnum;
    /**
     * 
     * @type {number}
     * @memberof CommodityDetailDTO
     */
    'selectionStatus'?: CommodityDetailDTOSelectionStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof CommodityDetailDTO
     */
    'selectionReason'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityDetailDTO
     */
    'createTime'?: string;
    /**
     * 
     * @type {Array<number>}
     * @memberof CommodityDetailDTO
     */
    'properties'?: Array<number>;
    /**
     * 
     * @type {Array<CommoditySkuDTO>}
     * @memberof CommodityDetailDTO
     */
    'skus'?: Array<CommoditySkuDTO>;
    /**
     * 
     * @type {Array<CommodityImageDTO>}
     * @memberof CommodityDetailDTO
     */
    'images'?: Array<CommodityImageDTO>;
    /**
     * 
     * @type {Array<CommodityFileDTO>}
     * @memberof CommodityDetailDTO
     */
    'files'?: Array<CommodityFileDTO>;
}

export const CommodityDetailDTOProductStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type CommodityDetailDTOProductStatusEnum = typeof CommodityDetailDTOProductStatusEnum[keyof typeof CommodityDetailDTOProductStatusEnum];
export const CommodityDetailDTOSelectionStatusEnum = {
    PENDING: 0,
    APPROVED: 1,
    REJECTED: 2
} as const;

export type CommodityDetailDTOSelectionStatusEnum = typeof CommodityDetailDTOSelectionStatusEnum[keyof typeof CommodityDetailDTOSelectionStatusEnum];


