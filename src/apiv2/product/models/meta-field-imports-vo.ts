/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 数据
 * @export
 * @interface MetaFieldImportsVO
 */
export interface MetaFieldImportsVO {
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'spuCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'spuName'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'spuId'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'productSize'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'delivery'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'assembly'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'color'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'material'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'weight'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'packageSize'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'warning'?: string;
    /**
     * 
     * @type {string}
     * @memberof MetaFieldImportsVO
     */
    'errorMsg'?: string;
}

