/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface PropertyMappingType
 */
export interface PropertyMappingType {
    /**
     * 
     * @type {string}
     * @memberof PropertyMappingType
     */
    'categoryType'?: PropertyMappingTypeCategoryTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof PropertyMappingType
     */
    'categoryName'?: string;
}

export const PropertyMappingTypeCategoryTypeEnum = {
    STANDER_CATEGORY: '0',
    CUSTOM_CATEGORY: '1'
} as const;

export type PropertyMappingTypeCategoryTypeEnum = typeof PropertyMappingTypeCategoryTypeEnum[keyof typeof PropertyMappingTypeCategoryTypeEnum];


