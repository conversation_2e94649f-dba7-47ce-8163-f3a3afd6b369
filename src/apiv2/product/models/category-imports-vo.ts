/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ImportsErrorVO } from './imports-error-vo';

/**
 * 数据
 * @export
 * @interface CategoryImportsVO
 */
export interface CategoryImportsVO {
    /**
     * 
     * @type {number}
     * @memberof CategoryImportsVO
     */
    'totalCount'?: number;
    /**
     * 
     * @type {number}
     * @memberof CategoryImportsVO
     */
    'successCount'?: number;
    /**
     * 
     * @type {number}
     * @memberof CategoryImportsVO
     */
    'errorCount'?: number;
    /**
     * 
     * @type {Array<ImportsErrorVO>}
     * @memberof CategoryImportsVO
     */
    'errors'?: Array<ImportsErrorVO>;
    /**
     * 
     * @type {boolean}
     * @memberof CategoryImportsVO
     */
    'result'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CategoryImportsVO
     */
    'message'?: string;
}

