/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MetaFieldImportsVO } from './meta-field-imports-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOListMetaFieldImportsVO
 */
export interface ResultVOListMetaFieldImportsVO {
    /**
     * 错误码
     * @type {number}
     * @memberof ResultVOListMetaFieldImportsVO
     */
    'code'?: number;
    /**
     * 错误消息
     * @type {string}
     * @memberof ResultVOListMetaFieldImportsVO
     */
    'message'?: string;
    /**
     * 数据
     * @type {Array<MetaFieldImportsVO>}
     * @memberof ResultVOListMetaFieldImportsVO
     */
    'data'?: Array<MetaFieldImportsVO>;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOListMetaFieldImportsVO
     */
    'success'?: boolean;
}

