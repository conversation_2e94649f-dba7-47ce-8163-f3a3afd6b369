/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface CommodityImageDTO
 */
export interface CommodityImageDTO {
    /**
     * 
     * @type {number}
     * @memberof CommodityImageDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommodityImageDTO
     */
    'skuCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityImageDTO
     */
    'imageKey'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommodityImageDTO
     */
    'imageUrl'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommodityImageDTO
     */
    'imageType'?: CommodityImageDTOImageTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof CommodityImageDTO
     */
    'filename'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CommodityImageDTO
     */
    'isCover'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CommodityImageDTO
     */
    'imageSize'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommodityImageDTO
     */
    'imageSizeKb'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommodityImageDTO
     */
    'order'?: number;
}

export const CommodityImageDTOImageTypeEnum = {
    PRODUCT_MAIN_IMAGE: 0,
    PRODUCT_SKU_IMAGE: 1,
    PRODUCT_DETAIL_IMAGE: 2
} as const;

export type CommodityImageDTOImageTypeEnum = typeof CommodityImageDTOImageTypeEnum[keyof typeof CommodityImageDTOImageTypeEnum];


