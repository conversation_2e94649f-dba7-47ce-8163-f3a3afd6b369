/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface ProductSkuPriceDTO
 */
export interface ProductSkuPriceDTO {
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'skuId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'coefficientId'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'purchasePrice'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'purchasePriceExFreight'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductSkuPriceDTO
     */
    'country'?: ProductSkuPriceDTOCountryEnum;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'oceanFreightEstimate'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'lastMileFreightEstimate'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductSkuPriceDTO
     */
    'baseCurrency'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductSkuPriceDTO
     */
    'targetCurrency'?: string;
    /**
     * 
     * @type {number}
     * @memberof ProductSkuPriceDTO
     */
    'calculatedPrice'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductSkuPriceDTO
     */
    'expirationTime'?: string;
    /**
     * 
     * @type {string}
     * @memberof ProductSkuPriceDTO
     */
    'priceTime'?: string;
}

export const ProductSkuPriceDTOCountryEnum = {
    JAPAN: 'JP',
    KOREA: 'KR',
    UNITED_STATES: 'US',
    CHINA: 'CN',
    UNITED_KINGDOM: 'GB',
    FRANCE: 'FR',
    GERMANY: 'DE',
    ITALY: 'IT',
    SPAIN: 'ES',
    CANADA: 'CA',
    AUSTRALIA: 'AU',
    NEW_ZEALAND: 'NZ',
    SINGAPORE: 'SG',
    MALAYSIA: 'MY',
    THAILAND: 'TH',
    VIETNAM: 'VN'
} as const;

export type ProductSkuPriceDTOCountryEnum = typeof ProductSkuPriceDTOCountryEnum[keyof typeof ProductSkuPriceDTOCountryEnum];


