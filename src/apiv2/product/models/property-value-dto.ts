/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface PropertyValueDTO
 */
export interface PropertyValueDTO {
    /**
     * 
     * @type {number}
     * @memberof PropertyValueDTO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof PropertyValueDTO
     */
    'propertyId'?: number;
    /**
     * 
     * @type {string}
     * @memberof PropertyValueDTO
     */
    'valueName'?: string;
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof PropertyValueDTO
     */
    'i18nValueNames'?: { [key: string]: string; };
    /**
     * 
     * @type {string}
     * @memberof PropertyValueDTO
     */
    'valueCode'?: string;
    /**
     * 
     * @type {{ [key: string]: object; }}
     * @memberof PropertyValueDTO
     */
    'attrs'?: { [key: string]: object; };
}

