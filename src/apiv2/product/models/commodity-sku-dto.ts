/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { DictValueDTO } from './dict-value-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuPriceDTO } from './product-sku-price-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuSpecDTO } from './product-sku-spec-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueDTO } from './property-value-dto';

/**
 * 
 * @export
 * @interface CommoditySkuDTO
 */
export interface CommoditySkuDTO {
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'skuCode'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'status'?: CommoditySkuDTOStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'thumbnail'?: string;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'supplyPrice'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'marketPrice'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'discountRate'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'promoPrice'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'coefficient'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'installationFee'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommoditySkuDTO
     */
    'netWeight'?: number;
    /**
     * 
     * @type {DictValueDTO}
     * @memberof CommoditySkuDTO
     */
    'color'?: DictValueDTO;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'spec1'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'spec2'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'spec3'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'spec4'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommoditySkuDTO
     */
    'spec5'?: string;
    /**
     * 
     * @type {DictValueDTO}
     * @memberof CommoditySkuDTO
     */
    'priMatLv1'?: DictValueDTO;
    /**
     * 
     * @type {DictValueDTO}
     * @memberof CommoditySkuDTO
     */
    'priMatLv2'?: DictValueDTO;
    /**
     * 
     * @type {DictValueDTO}
     * @memberof CommoditySkuDTO
     */
    'secMatLv1'?: DictValueDTO;
    /**
     * 
     * @type {DictValueDTO}
     * @memberof CommoditySkuDTO
     */
    'secMatLv2'?: DictValueDTO;
    /**
     * 
     * @type {Array<ProductSkuSpecDTO>}
     * @memberof CommoditySkuDTO
     */
    'specs'?: Array<ProductSkuSpecDTO>;
    /**
     * 
     * @type {Array<PropertyValueDTO>}
     * @memberof CommoditySkuDTO
     */
    'properties'?: Array<PropertyValueDTO>;
    /**
     * 
     * @type {Array<ProductSkuPriceDTO>}
     * @memberof CommoditySkuDTO
     */
    'prices'?: Array<ProductSkuPriceDTO>;
}

export const CommoditySkuDTOStatusEnum = {
    DRAFT: 0,
    CHECKING: 1,
    ON_SHELF: 2,
    OFF_SHELVES: 3,
    FREEZE: 4,
    DELETED: 5
} as const;

export type CommoditySkuDTOStatusEnum = typeof CommoditySkuDTOStatusEnum[keyof typeof CommoditySkuDTOStatusEnum];


