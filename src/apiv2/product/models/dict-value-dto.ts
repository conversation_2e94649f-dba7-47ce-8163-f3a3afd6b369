/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface DictValueDTO
 */
export interface DictValueDTO {
    /**
     * 
     * @type {number}
     * @memberof DictValueDTO
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DictValueDTO
     */
    'valueName'?: string;
    /**
     * 
     * @type {string}
     * @memberof DictValueDTO
     */
    'valueCode'?: string;
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof DictValueDTO
     */
    'i18nValueNames'?: { [key: string]: string; };
}

