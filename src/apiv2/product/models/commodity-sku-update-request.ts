/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuSpecDTO } from './product-sku-spec-dto';

/**
 * 商品SKU更新参数
 * @export
 * @interface CommoditySkuUpdateRequest
 */
export interface CommoditySkuUpdateRequest {
    /**
     * SKU ID（更新时必填）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'skuId'?: number;
    /**
     * 属性列表
     * @type {Array<number>}
     * @memberof CommoditySkuUpdateRequest
     */
    'properties'?: Array<number>;
    /**
     * 颜色
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'color'?: number;
    /**
     * 供应价（元）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'supplyPrice'?: number;
    /**
     * 市场价（元）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'marketPrice'?: number;
    /**
     * 促销价（元）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'promoPrice'?: number;
    /**
     * 折扣率
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'discountRate'?: number;
    /**
     * 价格系数
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'coefficient'?: number;
    /**
     * 安装服务费
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'installationFee'?: number;
    /**
     * 主材质（一级）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'priMatLv1'?: number;
    /**
     * 主材质（二级）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'priMatLv2'?: number;
    /**
     * 次材质（一级）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'secMatLv1'?: number;
    /**
     * 次材质（二级）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'secMatLv2'?: number;
    /**
     * 规格列表
     * @type {Array<ProductSkuSpecDTO>}
     * @memberof CommoditySkuUpdateRequest
     */
    'specs'?: Array<ProductSkuSpecDTO>;
    /**
     * 安装难易度
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'installDifficulty'?: number;
    /**
     * 规格1
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'spec1'?: string;
    /**
     * 规格2
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'spec2'?: string;
    /**
     * 规格3
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'spec3'?: string;
    /**
     * 规格4
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'spec4'?: string;
    /**
     * 规格5
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'spec5'?: string;
}

