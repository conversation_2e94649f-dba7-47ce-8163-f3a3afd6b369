import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'
import { PAGE_PERMISSION } from '@/enums/permission'

const routes: Array<RouteRecordRaw> = [
  {
    // 运营管理
    component: Layout,
    path: '/operation',
    name: 'operation',
    children: [
      {
        path: 'list',
        name: 'MerchandiseList',
        meta: {
          title: '上架运营',
          KeepAlive: true,
          permission: PAGE_PERMISSION.OPERATION,
        },
        component: () => import('@/views/Operation/Merchandise/MerchandiseList.vue'),
      },
    ],
  },
]
export default routes
