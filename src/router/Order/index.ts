import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'
import { PAGE_PERMISSION } from '@/enums/permission'

const routes: Array<RouteRecordRaw> = [
  {
    // 订单履约监控
    component: Layout,
    path: '/order',
    name: 'order',
    children: [
      {
        path: 'monitor',
        name: 'OrderMonitor',
        meta: { title: '订单履约监控', KeepAlive: true, permission: PAGE_PERMISSION.ORDER_IMPORT},
        component: () => import('@/views/Order/OrderMonitor.vue'),
      },
    ],
  },
]

export default routes
