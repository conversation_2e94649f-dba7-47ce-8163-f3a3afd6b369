import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'
import { PAGE_PERMISSION } from '@/enums/permission'
const routes: Array<RouteRecordRaw> = [
  {
    // 下载中心
    component: Layout,
    path: '/download-center',
    name: 'download-center',
    children: [
      {
        path: '',
        name: 'DownloadCenter',
        meta: { title: '下载中心', KeepAlive: true, permission: PAGE_PERMISSION.DOWNLOAD_CENTER },
        component: () => import('@/views/File/DownloadCenter.vue'),
      },
    ],
  },
]
export default routes
