import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'
import { PAGE_PERMISSION } from '@/enums/permission'

const routes: Array<RouteRecordRaw> = [
  {
    // 工单
    component: Layout,
    path: '/ticket',
    name: 'ticket',
    children: [
      {
        path: 'workbench',
        name: 'Workbench',
        meta: { title: '工作台', KeepAlive: true },
        component: () => import('@/views/Ticket/Workbench.vue'),
      },
      {
        path: 'service',
        name: 'ServiceTicket',
        meta: { title: '服务工单', KeepAlive: true, permission: PAGE_PERMISSION.SERVICE_TICKET },
        component: () => import('@/views/Ticket/Service.vue'),
      },
      {
        path: 'detail/:id',
        name: 'TicketDetail',
        meta: {
          title: (route: RouteLocationNormalized) => {
            const ticketId = route.params.id as string
            return ticketId ? `工单详情 - ${ticketId}` : '工单详情'
          },
        },
        component: () => import('@/views/Ticket/Workbench.vue'),
      },
    ],
  },
]

export default routes
