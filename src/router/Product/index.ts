import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'
import { useProductStore } from '@/stores/Product'
import { PAGE_PERMISSION } from '@/enums/permission'

const routes: Array<RouteRecordRaw> = [
  {
    // 商品
    component: Layout,
    path: '/product',
    name: 'product',
    meta: { permission: PAGE_PERMISSION.PRODUCT },
    children: [
      {
        path: 'list',
        name: 'ProductList',
        meta: {
          title: '产品管理',
          KeepAlive: true,
          permission: PAGE_PERMISSION.PRODUCT,
        },
        component: () => import('@/views/Product/Product/ProductList.vue'),
      },
      {
        path: 'create',
        name: 'ProductCreate',
        meta: { title: '创建产品', permission: PAGE_PERMISSION.PRODUCT_CREATE },
        component: () => import('@/views/Product/Product/ProductCreate.vue'),
      },
      {
        path: 'edit/:id',
        name: 'ProductEdit',
        meta: {
          title: (route: RouteLocationNormalized) => {
            const productStore = useProductStore()
            const productId = route.params.id as string
            const productName = productStore.getProductName(productId)
            return productName ? `编辑产品 - ${productName}` : '编辑产品'
          },
          permission: PAGE_PERMISSION.PRODUCT_UPDATE
        },
        component: () => import('@/views/Product/Product/ProductEdit.vue'),
      },
      {
        path: 'detail/:id',
        name: 'ProductDetail',
        meta: {
          title: (route: RouteLocationNormalized) => {
            const productStore = useProductStore()
            const productId = route.params.id as string
            const productName = productStore.getProductName(productId)
            return productName ? `产品详情 - ${productName}` : '产品详情'
          },
          performance: PAGE_PERMISSION.PRODUCT_DETAIL,
        },
        component: () => import('@/views/Product/Product/ProductDetail.vue'),
      },
      {
        path: 'category',
        name: 'CategoryList',
        meta: { title: '类目管理', KeepAlive: true, permission: PAGE_PERMISSION.CATEGORY },
        component: () => import('@/views/Product/Category/CategoryList.vue'),
      },
    ],
  },
]
export default routes
