/**
 * 页面权限枚举 —— 仅用于页面级别的权限控制
 */
export enum PAGE_PERMISSION {
  /** 产品管理 */
  PRODUCT = 'product:mngmt:query',

  /** 产品详情 */
  PRODUCT_DETAIL = 'product:mngmt:detail',

  /** 产品创建 */
  PRODUCT_CREATE = 'product:mngmt:create',

  /** 编辑商品 */
  PRODUCT_UPDATE = 'product:mngmt:edit',

  /** 类目管理 */
  CATEGORY = 'category:mngmt:tree',

  /** 上架运营 */
  OPERATION = 'commodity:mngmt:query',

  /** 服务工单 */
  SERVICE_TICKET = 'ticket:mngmt:start',

  /** 订单履约数据 */
  ORDER_IMPORT = 'fulfillment:mngmt:query',

  /** 下载中心 */
  DOWNLOAD_CENTER = 'ipep:mngmt:query',
}

/**
 * 按钮权限枚举
 */
export enum BUTTON_PERMISSION {
  /** 查看商品详情 */
  PRODUCT_DETAIL = 'product:mngmt:detail',
  /** 产品建档 */
  PRODUCT_CREATE = 'product:mngmt:create',
  /** 批量导入建档 —— 待实现 */
  PRODUCT_IMPORT = 'product:mngmt:import',
  /** 发起工单 & 生成工单 */
  PRODUCT_CREATE_TICKET = 'product:mngmt:create:ticket',
  /** 编辑标签 */
  PRODUCT_BATCH_UPDATE_PROPERTIES = 'product:mngmt:properties:update',
  /** 删除 & 批量删除商品 */
  PRODUCT_DELETE = 'commodity:mngmt:delete',
  /** 上架/下架 */
  PRODUCT_BATCH_UPDATE_STATUS = 'commodity:mngmt:status:switch',
  /** 编辑商品 */
  PRODUCT_UPDATE = 'product:mngmt:edit',
  /** 查看操作日志 */
  PRODUCT_LOG_QUERY = 'product:mngmt:log:query',
  /** 导出 */
  PRODUCT_EXPORT = 'file:export:create',

  /** 添加类目 */
  CATEGORY_CREATE = 'category:mngmt:add',
  /** 删除类目 */
  CATEGORY_DELETE = 'category:mngmt:delete',
  /** 编辑类目 */
  CATEGORY_UPDATE = 'category:mngmt:edit',

  /** 设置选品状态 */
  COMMODITY_SELECTION_STATUS_UPDATE = 'commodity:mngmt:selection-status:switch',
  /** 设置商品状态 */
  COMMODITY_PLATFORM_STATUS_UPDATE = 'commodity:mngmt:platform-status:switch',
  /** 设置售价 */
  COMMODITY_SKU_PRICES_UPDATE = 'commodity:mngmt:sku:prices:set',
  /** 运营商品详情 */
  COMMODITY_DETAIL = 'commodity:mngmt:detail',
  /** 编辑 */
  COMMODITY_EDIT = 'commodity:mngmt:edit',
  /** 导出 */
  COMMODITY_EXPORT = 'commodity:mngmt:export',

  /** 查看工单日志 */
  TICKET_LOG_QUERY = 'ticket:mngmt:log:query',

  /** 订单数据导入 */
  ORDER_IMPORT = 'fulfillment:mngmt:import',

  /** 下载文件 */
  FILE_DOWNLOAD = 'ipep:mngmt:download',
}
